/**
 * Test script to verify ignoreExpiration functionality
 */

import {
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate (which appears expired due to system date)
const USER_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Test payload
const TEST_PAYLOAD = {
  pin: "1234",
  cardId: "****************",
  cardType: "VISA"
};

async function testIgnoreExpiration() {
  console.log('=== Testing Ignore Expiration Feature ===\n');

  console.log('Current system date:', new Date().toString());
  console.log('Certificate expires: May 23, 2025 20:43:35');
  console.log('');

  // Test 1: Normal encryption (should fail due to expiration)
  console.log('1. Testing normal encryption (with expiration check):');
  try {
    const result1 = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, USER_CERTIFICATE);
    
    if (result1.success) {
      console.log('   ✅ SUCCESS (unexpected)');
    } else {
      console.log('   ❌ FAILED (expected due to expiration)');
      console.log(`   Error: ${result1.error?.code} - ${result1.error?.message}`);
    }
  } catch (error) {
    console.log('   ❌ EXCEPTION:', error);
  }

  console.log('');

  // Test 2: Encryption with ignoreExpiration = true
  console.log('2. Testing encryption with ignoreExpiration = true:');
  try {
    const result2 = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, USER_CERTIFICATE, true);
    
    if (result2.success) {
      console.log('   ✅ SUCCESS! Encryption worked with ignoreExpiration');
      console.log(`   Encrypted data length: ${result2.data?.length} characters`);
      console.log(`   First 50 chars: ${result2.data?.substring(0, 50)}...`);
    } else {
      console.log('   ❌ FAILED (unexpected)');
      console.log(`   Error: ${formatEncryptionError(result2.error!)}`);
    }
  } catch (error) {
    console.log('   ❌ EXCEPTION:', error);
  }

  console.log('');

  // Test 3: Basic function with ignoreExpiration
  console.log('3. Testing basic payloadEncryptionFactory with ignoreExpiration = true:');
  try {
    const result3 = await payloadEncryptionFactory(TEST_PAYLOAD, USER_CERTIFICATE, true);
    
    if (result3) {
      console.log('   ✅ SUCCESS! Basic function worked with ignoreExpiration');
      console.log(`   Encrypted data length: ${result3.length} characters`);
      console.log(`   First 50 chars: ${result3.substring(0, 50)}...`);
    } else {
      console.log('   ❌ FAILED - returned null');
    }
  } catch (error) {
    console.log('   ❌ EXCEPTION:', error);
  }

  console.log('');

  // Test 4: Certificate parsing with ignoreExpiration
  console.log('4. Testing certificate parsing with ignoreExpiration = true:');
  try {
    const certInfo = parsePEMCertificate(USER_CERTIFICATE, true);
    
    console.log(`   Certificate valid: ${certInfo.valid}`);
    console.log(`   Key size: ${certInfo.keySize} bits`);
    console.log(`   Algorithm: ${certInfo.algorithm}`);
    console.log(`   Expiration: ${certInfo.expirationDate}`);
    console.log(`   Is Expired: ${certInfo.isExpired}`);
    
    if (certInfo.valid) {
      console.log('   ✅ Certificate parsing successful with ignoreExpiration');
    } else {
      console.log('   ❌ Certificate parsing still failed');
    }
  } catch (error) {
    console.log('   ❌ EXCEPTION:', error);
  }

  console.log('\n=== Test Complete ===');
  console.log('\n💡 Usage in your code:');
  console.log('```typescript');
  console.log('// Ignore certificate expiration for testing');
  console.log('const encrypted = await payloadEncryptionFactory(payload, certificate, true);');
  console.log('');
  console.log('// Or with detailed errors');
  console.log('const result = await payloadEncryptionFactoryWithErrors(payload, certificate, true);');
  console.log('```');
}

testIgnoreExpiration().catch(console.error);