/**
 * Certificate auto-formatter for K6 scripts
 * This function automatically formats certificates that are on one line
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors 
} from './src/utils/encryption';

/**
 * Automatically format a certificate string to proper PEM format
 * @param certString - Certificate string (can be on one line or properly formatted)
 * @returns Properly formatted PEM certificate
 */
export function autoFormatCertificate(certString: string): string {
  if (!certString || typeof certString !== 'string') {
    throw new Error('Certificate must be a non-empty string');
  }

  // Remove all whitespace and line breaks
  let cleanCert = certString.replace(/\s/g, '');
  
  // Check for BEGIN and END markers
  const beginMarker = '-----BEGINCERTIFICATE-----';
  const endMarker = '-----ENDCERTIFICATE-----';
  
  const beginIndex = cleanCert.indexOf(beginMarker);
  const endIndex = cleanCert.indexOf(endMarker);
  
  if (beginIndex === -1 || endIndex === -1) {
    throw new Error('Invalid certificate: missing BEGIN CERTIFICATE or END CERTIFICATE markers');
  }
  
  // Extract the base64 content between markers
  const certContent = cleanCert.substring(beginIndex + beginMarker.length, endIndex);
  
  if (!certContent || certContent.length === 0) {
    throw new Error('Invalid certificate: no content between markers');
  }
  
  // Validate base64 content
  if (!/^[A-Za-z0-9+/=]+$/.test(certContent)) {
    throw new Error('Invalid certificate: content contains invalid base64 characters');
  }
  
  // Split into 64-character lines (standard PEM format)
  const lines = [];
  for (let i = 0; i < certContent.length; i += 64) {
    lines.push(certContent.substring(i, i + 64));
  }
  
  // Reconstruct with proper formatting
  return `-----BEGIN CERTIFICATE-----\n${lines.join('\n')}\n-----END CERTIFICATE-----`;
}

/**
 * Enhanced encryption function that auto-formats certificates
 * @param payload - Payload to encrypt
 * @param certificate - Certificate (will be auto-formatted if needed)
 * @param ignoreExpiration - Whether to ignore certificate expiration
 * @returns Encrypted data or null
 */
export async function encryptWithAutoFormat(
  payload: any, 
  certificate: string, 
  ignoreExpiration: boolean = false
): Promise<string | null> {
  try {
    // Try to auto-format the certificate
    const formattedCert = autoFormatCertificate(certificate);
    
    // Use the formatted certificate for encryption
    return await payloadEncryptionFactory(payload, formattedCert, ignoreExpiration);
  } catch (error) {
    console.error('Certificate auto-formatting failed:', error);
    
    // Fallback: try with original certificate
    console.log('Trying with original certificate format...');
    return await payloadEncryptionFactory(payload, certificate, ignoreExpiration);
  }
}

/**
 * Enhanced encryption function with detailed errors and auto-formatting
 */
export async function encryptWithAutoFormatAndErrors(
  payload: any, 
  certificate: string, 
  ignoreExpiration: boolean = false
) {
  try {
    // Try to auto-format the certificate
    const formattedCert = autoFormatCertificate(certificate);
    console.log('Certificate auto-formatted successfully');
    
    // Use the formatted certificate for encryption
    return await payloadEncryptionFactoryWithErrors(payload, formattedCert, ignoreExpiration);
  } catch (formatError) {
    console.error('Certificate auto-formatting failed:', formatError);
    
    // Fallback: try with original certificate
    console.log('Trying with original certificate format...');
    return await payloadEncryptionFactoryWithErrors(payload, certificate, ignoreExpiration);
  }
}

// Test the auto-formatter
async function testAutoFormatter() {
  console.log('🔧 Testing Certificate Auto-Formatter\n');

  // Your certificate as it appears in the logs (all on one line)
  const MALFORMED_CERTIFICATE = `-----BEGIN CERTIFICATE-----MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQELBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQxFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMyMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlVbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7VXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvRxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsIFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNNN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQNrrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWyAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvGSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/QmKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxBoI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzCwW5lyO+/qcFe-----END CERTIFICATE-----`;

  const PAYLOAD = {
    "pin": "1234",
    "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
    "cardType": "DebitCard",
    "otp": "123456"
  };

  console.log('1. Testing auto-formatter:');
  try {
    const formatted = autoFormatCertificate(MALFORMED_CERTIFICATE);
    console.log('   ✅ Certificate formatted successfully');
    console.log('   First few lines:');
    console.log('   ' + formatted.split('\n').slice(0, 4).join('\n   '));
    console.log('   ...');
  } catch (error) {
    console.log(`   ❌ Formatting failed: ${error}`);
    return;
  }

  console.log('\n2. Testing encryption with auto-format:');
  const result = await encryptWithAutoFormatAndErrors(PAYLOAD, MALFORMED_CERTIFICATE, true);
  
  if (result.success) {
    console.log('   🎉 SUCCESS! Auto-format + encryption worked!');
    console.log(`   Encrypted length: ${result.data?.length} characters`);
    console.log(`   First 50 chars: ${result.data?.substring(0, 50)}...`);
  } else {
    console.log('   ❌ Encryption failed');
    console.log(`   Error: ${result.error?.code} - ${result.error?.message}`);
  }

  console.log('\n3. Testing basic auto-format function:');
  const basicResult = await encryptWithAutoFormat(PAYLOAD, MALFORMED_CERTIFICATE, true);
  
  if (basicResult) {
    console.log('   ✅ Basic auto-format encryption successful');
    console.log(`   Length: ${basicResult.length} characters`);
  } else {
    console.log('   ❌ Basic auto-format encryption failed');
  }

  console.log('\n🎯 SOLUTION FOR YOUR K6 SCRIPT:');
  console.log('Add this function to your K6 script and use it instead of the regular encryption:');
  console.log('');
  console.log('```javascript');
  console.log('// Auto-format certificate function');
  console.log('function autoFormatCertificate(certString) {');
  console.log('  const cleanCert = certString.replace(/\\s/g, "");');
  console.log('  const beginMarker = "-----BEGINCERTIFICATE-----";');
  console.log('  const endMarker = "-----ENDCERTIFICATE-----";');
  console.log('  const beginIndex = cleanCert.indexOf(beginMarker);');
  console.log('  const endIndex = cleanCert.indexOf(endMarker);');
  console.log('  const certContent = cleanCert.substring(beginIndex + beginMarker.length, endIndex);');
  console.log('  const lines = [];');
  console.log('  for (let i = 0; i < certContent.length; i += 64) {');
  console.log('    lines.push(certContent.substring(i, i + 64));');
  console.log('  }');
  console.log('  return `-----BEGIN CERTIFICATE-----\\n${lines.join("\\n")}\\n-----END CERTIFICATE-----`;');
  console.log('}');
  console.log('');
  console.log('// In your main function:');
  console.log('const rawCertificate = "your-one-line-certificate-here";');
  console.log('const formattedCertificate = autoFormatCertificate(rawCertificate);');
  console.log('const encrypted = await payloadEncryptionFactory(payload, formattedCertificate, true);');
  console.log('```');
}

testAutoFormatter().catch(console.error);