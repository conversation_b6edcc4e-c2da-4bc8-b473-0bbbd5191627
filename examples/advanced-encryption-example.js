/**
 * Advanced K6 Encryption Example with <PERSON><PERSON><PERSON> Handling
 * 
 * This example demonstrates advanced usage of the K6 encryption utility
 * including detailed error handling, performance monitoring, and various
 * payload scenarios.
 * 
 * Usage:
 *   k6 run examples/advanced-encryption-example.js
 * 
 * Environment Variables:
 *   ENCRYPTION_CERTIFICATE - PEM-formatted X.509 certificate (required)
 *   API_BASE_URL - Base URL for API calls (optional)
 *   DEBUG_MODE - Enable debug logging (optional)
 */

import http from 'k6/http';
import { check, group, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';
import { 
  payloadEncryptionFactoryWithErrors,
  EncryptionErrorType,
  formatEncryptionError,
  validateEncryptionInputs,
  comprehensiveValidation
} from '../dist/utils/encryption.js';

// Custom metrics
const encryptionSuccessRate = new Rate('encryption_success_rate');
const encryptionErrorRate = new Rate('encryption_error_rate');
const encryptionDuration = new Trend('encryption_duration_ms');
const payloadSizeMetric = new Trend('payload_size_bytes');
const apiCallDuration = new Trend('api_call_duration_ms');
const certificateErrors = new Counter('certificate_errors');
const payloadErrors = new Counter('payload_errors');

// Test configuration
export const options = {
  stages: [
    { duration: '1m', target: 5 },   // Ramp up
    { duration: '3m', target: 10 },  // Stay at 10 users
    { duration: '1m', target: 0 },   // Ramp down
  ],
  thresholds: {
    encryption_success_rate: ['rate>0.95'],
    encryption_error_rate: ['rate<0.05'],
    encryption_duration_ms: ['avg<500', 'p(95)<1000'],
    api_call_duration_ms: ['avg<2000', 'p(95)<5000'],
    checks: ['rate>0.9'],
  },
};

// Configuration
const DEBUG_MODE = __ENV.DEBUG_MODE === 'true';
const API_BASE_URL = __ENV.API_BASE_URL || 'https://httpbin.org';

// Sample certificate (replace with your own)
const DEFAULT_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==
-----END CERTIFICATE-----`;

/**
 * Generate test payloads of different sizes and complexities
 */
function generateTestPayloads() {
  const basePayload = {
    userId: `user_${__VU}`,
    sessionId: `session_${__VU}_${__ITER}`,
    timestamp: Date.now(),
    requestId: `req_${Math.random().toString(36).substr(2, 9)}`
  };

  return [
    // Small payload
    {
      name: 'small',
      data: {
        ...basePayload,
        action: 'ping'
      }
    },
    // Medium payload
    {
      name: 'medium',
      data: {
        ...basePayload,
        action: 'login',
        credentials: {
          username: 'testuser',
          pin: '1234'
        },
        metadata: {
          userAgent: 'K6-Test',
          ipAddress: '***********'
        }
      }
    },
    // Large payload (close to size limits)
    {
      name: 'large',
      data: {
        ...basePayload,
        action: 'transaction',
        transaction: {
          amount: 99.99,
          currency: 'USD',
          description: 'Test transaction for load testing purposes',
          merchant: {
            id: 'merchant_123',
            name: 'Test Merchant Store',
            category: 'retail'
          }
        },
        paymentMethod: {
          type: 'card',
          cardNumber: '****************',
          expiryMonth: '12',
          expiryYear: '2025',
          cvv: '123'
        }
      }
    }
  ];
}

/**
 * Perform encryption with comprehensive error handling
 */
async function performEncryptionWithErrorHandling(payload, certificate, payloadName) {
  const startTime = Date.now();
  
  try {
    // Pre-validation
    if (DEBUG_MODE) {
      console.log(`[DEBUG] Validating ${payloadName} payload...`);
      const validation = comprehensiveValidation(payload, certificate);
      if (!validation.isValid) {
        console.log(`[DEBUG] Validation issues found:`, validation.errors.length);
      }
    }

    // Perform encryption with detailed error reporting
    const result = await payloadEncryptionFactoryWithErrors(payload, certificate);
    const duration = Date.now() - startTime;
    
    // Record metrics
    encryptionDuration.add(duration);
    payloadSizeMetric.add(JSON.stringify(payload).length);
    
    if (result.success) {
      encryptionSuccessRate.add(true);
      encryptionErrorRate.add(false);
      
      if (DEBUG_MODE) {
        console.log(`✓ ${payloadName} encryption successful in ${duration}ms`);
      }
      
      return {
        success: true,
        data: result.data,
        duration: duration
      };
    } else {
      // Handle specific error types
      encryptionSuccessRate.add(false);
      encryptionErrorRate.add(true);
      
      const error = result.error;
      const formattedError = formatEncryptionError(error);
      
      console.error(`✗ ${payloadName} encryption failed: ${formattedError}`);
      
      // Count specific error types
      switch (error.code) {
        case EncryptionErrorType.CERTIFICATE_EXPIRED:
        case EncryptionErrorType.INVALID_CERTIFICATE:
        case EncryptionErrorType.CERTIFICATE_PARSING_FAILED:
          certificateErrors.add(1);
          break;
        case EncryptionErrorType.INVALID_PAYLOAD:
        case EncryptionErrorType.PAYLOAD_TOO_LARGE:
        case EncryptionErrorType.SERIALIZATION_FAILED:
          payloadErrors.add(1);
          break;
      }
      
      return {
        success: false,
        error: error,
        duration: duration
      };
    }
  } catch (exception) {
    const duration = Date.now() - startTime;
    encryptionSuccessRate.add(false);
    encryptionErrorRate.add(true);
    
    console.error(`✗ ${payloadName} encryption exception:`, exception);
    
    return {
      success: false,
      error: { code: 'EXCEPTION', message: exception.toString() },
      duration: duration
    };
  }
}

/**
 * Simulate API call with encrypted payload
 */
async function makeEncryptedApiCall(encryptedData, payloadName) {
  const requestPayload = {
    encryptedData: encryptedData,
    requestId: `req_${__VU}_${__ITER}_${payloadName}`,
    timestamp: Date.now(),
    source: 'k6-load-test'
  };

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'K6-Encryption-Test/1.0'
    },
    timeout: '10s'
  };

  const startTime = Date.now();
  
  try {
    const response = http.post(
      `${API_BASE_URL}/post`,
      JSON.stringify(requestPayload),
      params
    );
    
    const duration = Date.now() - startTime;
    apiCallDuration.add(duration);
    
    const success = check(response, {
      [`${payloadName}_api_status_ok`]: (r) => r.status >= 200 && r.status < 300,
      [`${payloadName}_api_response_time_ok`]: (r) => r.timings.duration < 5000,
      [`${payloadName}_api_has_response_body`]: (r) => r.body && r.body.length > 0,
    });
    
    if (DEBUG_MODE && success) {
      console.log(`✓ ${payloadName} API call successful in ${duration}ms (status: ${response.status})`);
    }
    
    return { success, response, duration };
  } catch (error) {
    console.error(`✗ ${payloadName} API call failed:`, error);
    return { success: false, error, duration: Date.now() - startTime };
  }
}

/**
 * Main test function
 */
export default async function () {
  const certificate = __ENV.ENCRYPTION_CERTIFICATE || DEFAULT_CERTIFICATE;
  
  if (certificate === DEFAULT_CERTIFICATE && __ITER === 0) {
    console.warn('Using default test certificate. Set ENCRYPTION_CERTIFICATE for production.');
  }

  const testPayloads = generateTestPayloads();
  
  // Test each payload type
  for (const testCase of testPayloads) {
    await group(`${testCase.name} payload encryption`, async () => {
      // Encrypt the payload
      const encryptionResult = await performEncryptionWithErrorHandling(
        testCase.data, 
        certificate, 
        testCase.name
      );
      
      // Validate encryption result
      const encryptionChecks = check(encryptionResult, {
        [`${testCase.name}_encryption_successful`]: (r) => r.success === true,
        [`${testCase.name}_encryption_duration_acceptable`]: (r) => r.duration < 1000,
      });
      
      // If encryption successful, make API call
      if (encryptionResult.success && encryptionResult.data) {
        await group(`${testCase.name} API integration`, async () => {
          const apiResult = await makeEncryptedApiCall(encryptionResult.data, testCase.name);
          
          check(apiResult, {
            [`${testCase.name}_api_integration_successful`]: (r) => r.success === true,
          });
        });
      } else {
        // Log detailed error information for failed encryptions
        if (encryptionResult.error) {
          console.error(`Detailed error for ${testCase.name}:`, encryptionResult.error);
        }
      }
    });
    
    // Small delay between different payload types
    sleep(0.1);
  }
  
  // Overall iteration delay
  sleep(1);
}

/**
 * Setup function - runs once per VU at start
 */
export function setup() {
  console.log('=== Advanced K6 Encryption Example Setup ===');
  console.log('Configuration:');
  console.log(`  Virtual Users: ${options.stages.map(s => s.target).join(' → ')}`);
  console.log(`  API Base URL: ${API_BASE_URL}`);
  console.log(`  Debug Mode: ${DEBUG_MODE}`);
  
  const certificate = __ENV.ENCRYPTION_CERTIFICATE || DEFAULT_CERTIFICATE;
  console.log(`  Certificate Length: ${certificate.length} chars`);
  
  if (certificate === DEFAULT_CERTIFICATE) {
    console.warn('  ⚠️  Using default test certificate');
  }
  
  // Test certificate validity
  try {
    const validation = validateEncryptionInputs({ test: 'setup' }, certificate);
    if (validation.success) {
      console.log('  ✓ Certificate validation passed');
    } else {
      console.error('  ✗ Certificate validation failed:', validation.error);
    }
  } catch (error) {
    console.error('  ✗ Certificate validation error:', error);
  }
  
  console.log('=== Setup Complete ===\n');
}

/**
 * Teardown function - runs once after all VUs complete
 */
export function teardown(data) {
  console.log('\n=== Advanced K6 Encryption Example Results ===');
  console.log('Test completed successfully');
  console.log('Check the metrics for detailed performance data:');
  console.log('  - encryption_success_rate');
  console.log('  - encryption_duration_ms');
  console.log('  - payload_size_bytes');
  console.log('  - api_call_duration_ms');
  console.log('=== End Results ===');
}