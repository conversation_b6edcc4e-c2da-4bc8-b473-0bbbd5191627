/**
 * Basic K6 Encryption Example
 * 
 * This example demonstrates how to use the K6 encryption utility
 * in a simple load testing scenario.
 * 
 * Usage:
 *   k6 run examples/basic-encryption-example.js
 * 
 * Environment Variables:
 *   ENCRYPTION_CERTIFICATE - PEM-formatted X.509 certificate (required)
 */

import { check, sleep } from 'k6';
import { payloadEncryptionFactory } from '../dist/utils/encryption.js';

// Test configuration
export const options = {
  vus: 5,           // 5 virtual users
  duration: '30s',  // Run for 30 seconds
  thresholds: {
    checks: ['rate>0.9'], // 90% of checks should pass
  },
};

// Sample certificate for testing (replace with your own)
const DEFAULT_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==
-----END CERTIFICATE-----`;

export default async function () {
  // Get certificate from environment or use default
  const certificate = __ENV.ENCRYPTION_CERTIFICATE || DEFAULT_CERTIFICATE;
  
  // Create a unique payload for this iteration
  const payload = {
    userId: `user_${__VU}`,
    sessionId: `session_${__VU}_${__ITER}`,
    timestamp: Date.now(),
    action: "login",
    credentials: {
      pin: "1234",
      cardId: "****************",
      cardType: "VISA"
    }
  };

  console.log(`VU ${__VU}, Iteration ${__ITER}: Starting encryption`);

  try {
    // Encrypt the payload
    const startTime = Date.now();
    const encryptedData = await payloadEncryptionFactory(payload, certificate);
    const encryptionTime = Date.now() - startTime;

    // Validate the encryption result
    const encryptionSuccessful = check(encryptedData, {
      'encryption completed': (result) => result !== null,
      'result is string': (result) => typeof result === 'string',
      'result is not empty': (result) => result && result.length > 0,
      'result is valid base64': (result) => {
        if (!result) return false;
        try {
          // Test if it's valid base64
          atob(result);
          return true;
        } catch (e) {
          return false;
        }
      },
      'encryption time acceptable': () => encryptionTime < 1000, // Less than 1 second
    });

    if (encryptionSuccessful) {
      console.log(`✓ VU ${__VU}, Iter ${__ITER}: Encryption successful in ${encryptionTime}ms`);
      console.log(`  Encrypted data length: ${encryptedData.length} characters`);
      console.log(`  First 50 chars: ${encryptedData.substring(0, 50)}...`);
    } else {
      console.error(`✗ VU ${__VU}, Iter ${__ITER}: Encryption failed`);
    }

    // Simulate some processing time
    sleep(0.5);

  } catch (error) {
    console.error(`VU ${__VU}, Iter ${__ITER}: Exception during encryption:`, error);
    
    // Still run checks to record the failure
    check(null, {
      'no exceptions occurred': () => false,
    });
  }
}

// Setup function (runs once per VU)
export function setup() {
  console.log('=== K6 Encryption Example Setup ===');
  console.log('Virtual Users:', __ENV.K6_VUS || options.vus);
  console.log('Duration:', options.duration);
  
  // Validate certificate is available
  const certificate = __ENV.ENCRYPTION_CERTIFICATE || DEFAULT_CERTIFICATE;
  if (certificate === DEFAULT_CERTIFICATE) {
    console.warn('Using default test certificate. Set ENCRYPTION_CERTIFICATE environment variable for production use.');
  }
  
  console.log('Certificate length:', certificate.length, 'characters');
  console.log('=== Setup Complete ===');
}

// Teardown function (runs once after all VUs complete)
export function teardown(data) {
  console.log('=== K6 Encryption Example Complete ===');
}