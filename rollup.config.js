import typescript from "@rollup/plugin-typescript";
import resolve from "@rollup/plugin-node-resolve";
import commonjs from "@rollup/plugin-commonjs";

const commonConfig = {
  output: { dir: "dist", format: "es" },
  external: ["k6/http", "k6", "k6/execution", "k6/data", "k6/metrics", "k6/html", "k6/encoding"],
  plugins: [
    typescript(), 
    commonjs(), 
    resolve({
      preferBuiltins: false,
      browser: true
    })
  ],
};

export default [
  {
    input: "src/load-generic.ts",
    ...commonConfig,
  },
  {
    input: "src/load-outside-transfer.ts",
    ...commonConfig,
  },
  {
    input: "src/load-cc-movement.ts",
    ...commonConfig,
  },
  {
    input: "src/load-account-movement.ts",
    ...commonConfig,
  },
  {
    input: "src/load-account-movement-details.ts",
    ...commonConfig,
  },
  {
    input: "src/load-deposits.ts",
    ...commonConfig,
  },
  {
    input: "src/load-deposits-list.ts",
    ...commonConfig,
  },
  {
    input: "src/load-inside-transfer.ts",
    ...commonConfig,
  },
  {
    input: "src/load-creditcard-transfer.ts",
    ...commonConfig,
  },
  {
    input: "src/load-offline-request.ts",
    ...commonConfig,
  },
  {
    input: "src/load-cc-installment.ts",
    ...commonConfig,
  },
  {
    input: "src/load-cr2.ts",
    ...commonConfig,
  },
  {
    input: "src/endurance.ts",
    ...commonConfig,
  },
  {
    input: "src/smoke.ts",
    ...commonConfig,
  },
  {
    input: "src/load-bill-payment.ts",
    ...commonConfig,
  },
  {
    input: "src/load-full-run.ts",
    ...commonConfig,
  },
  {
    input: "src/load-debit-list.ts",
    ...commonConfig,
  },
  {
    input: "src/load-biometric-login.ts",
    ...commonConfig,
  },
  {
    input: "src/shock.ts",
    ...commonConfig,
  },
  // {
  //
  //   input: "src/load-stocks-list.ts",
  //   ...commonConfig,
  // },
  {

    input: "src/load-ipn.ts",
    ...commonConfig,
  },
    {

    input: "src/load-annex-download.ts",
    ...commonConfig,
  },
  {

    input: "src/load-card-management.ts",
    ...commonConfig,
  },
  {

    input: "src/load-debit-card-issuance.ts",
    ...commonConfig,
  },
  {

    input: "src/load-deposit-redemption.ts",
    ...commonConfig,
  },
  {
    input: "src/test-encryption-functionality.ts",
    ...commonConfig,
  },
  {
    input: "src/test-validation-simple.ts",
    ...commonConfig,
  },
  {
    input: "src/integration-test-encryption.ts",
    ...commonConfig,
  },
  {
    input: "src/performance-test-encryption.ts",
    ...commonConfig,
  },
  {
    input: "src/k6-compatibility-test.ts",
    ...commonConfig,
  },
  {
    input: "src/run-integration-tests.ts",
    ...commonConfig,
  },
];
