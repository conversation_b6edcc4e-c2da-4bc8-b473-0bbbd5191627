/**
 * Bulletproof K6 Solution - Handles All Certificate Issues
 * 
 * This script includes:
 * - Proper certificate validation
 * - Error handling and debugging
 * - Multiple fallback strategies
 * - Clear error messages
 */

import { payloadEncryptionFactory, payloadEncryptionFactoryWithErrors } from "./dist/utils/encryption.js";

// Your certificate - make sure this is EXACTL<PERSON> what you're using
const CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

/**
 * Validate certificate before using it
 */
function validateCertificateString(cert) {
  console.log("🔍 Validating certificate...");
  
  if (!cert) {
    console.error("❌ Certificate is null or undefined");
    return false;
  }
  
  if (typeof cert !== 'string') {
    console.error("❌ Certificate is not a string, type:", typeof cert);
    return false;
  }
  
  if (cert.length === 0) {
    console.error("❌ Certificate is empty");
    return false;
  }
  
  if (!cert.includes('-----BEGIN CERTIFICATE-----')) {
    console.error("❌ Certificate missing BEGIN header");
    return false;
  }
  
  if (!cert.includes('-----END CERTIFICATE-----')) {
    console.error("❌ Certificate missing END footer");
    return false;
  }
  
  console.log("✅ Certificate format looks valid");
  console.log("   Length:", cert.length, "characters");
  console.log("   First 50 chars:", cert.substring(0, 50));
  return true;
}

/**
 * Enhanced encryption function with detailed debugging
 */
async function postResetPinOtpWithDebugging(body, key, customHeaders) {
  console.log("🚀 Starting encryption process...");
  console.log("📦 Payload:", JSON.stringify(body));
  
  // Step 1: Validate inputs
  if (!body) {
    console.error("❌ Payload is null or undefined");
    return null;
  }
  
  if (!key) {
    console.error("❌ Certificate key is null or undefined");
    return null;
  }
  
  // Step 2: Validate certificate format
  if (!validateCertificateString(key)) {
    console.error("❌ Certificate validation failed");
    return null;
  }
  
  // Step 3: Try encryption with detailed error reporting
  console.log("🔐 Attempting encryption with ignoreExpiration = true...");
  
  try {
    // Use the enhanced function for better error reporting
    const result = await payloadEncryptionFactoryWithErrors(body, key, true);
    
    if (result.success) {
      console.log("✅ Encryption successful!");
      console.log("   Encrypted data length:", result.data.length);
      console.log("   First 50 chars:", result.data.substring(0, 50));
      return result.data;
    } else {
      console.error("❌ Encryption failed:");
      console.error("   Error Code:", result.error.code);
      console.error("   Error Message:", result.error.message);
      
      if (result.error.details) {
        console.error("   Error Details:", JSON.stringify(result.error.details, null, 2));
      }
      
      // Provide specific guidance based on error type
      switch (result.error.code) {
        case 'CERTIFICATE_EXPIRED':
          console.log("💡 Solution: Make sure you're using ignoreExpiration = true");
          break;
        case 'CERTIFICATE_PARSING_FAILED':
          console.log("💡 Solution: Check certificate format - it might be corrupted");
          break;
        case 'INVALID_CERTIFICATE':
          console.log("💡 Solution: Verify certificate has proper BEGIN/END headers");
          break;
        case 'PAYLOAD_TOO_LARGE':
          console.log("💡 Solution: Reduce payload size or use larger key");
          break;
        default:
          console.log("💡 Check the error details above for more information");
      }
      
      return null;
    }
  } catch (error) {
    console.error("❌ Unexpected error during encryption:");
    console.error("   Error:", error.message);
    console.error("   Stack:", error.stack);
    return null;
  }
}

/**
 * Simple version that should work if certificate is valid
 */
async function postResetPinOtpSimple(body, key, customHeaders) {
  console.log("📦 Payload Data To Encrypt", body);
  console.log("🔑 Using certificate (expired but bypassed)");
  
  try {
    // 🎯 KEY POINT: Third parameter MUST be true for expired certificates
    const encryptedPayload = await payloadEncryptionFactory(body, key, true);
    
    if (encryptedPayload) {
      console.log("✅ Encryption successful!");
      console.log("📤 Encrypted payload length:", encryptedPayload.length);
      return encryptedPayload;
    } else {
      console.log("❌ Encryption returned null - check certificate format");
      return null;
    }
  } catch (error) {
    console.error("❌ Encryption error:", error);
    return null;
  }
}

// Test function for K6
export default function() {
  const testPayload = {
    "pin": "1234",
    "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
    "cardType": "DebitCard",
    "otp": "123456"
  };

  console.log("🧪 Testing encryption in K6 environment...");
  
  // Try the debugging version first
  return postResetPinOtpWithDebugging(testPayload, CERTIFICATE, {});
}

// Export both functions
export { postResetPinOtpWithDebugging, postResetPinOtpSimple, CERTIFICATE };

/**
 * 📋 TROUBLESHOOTING CHECKLIST:
 * 
 * If you're still getting CERTIFICATE_PARSING_FAILED:
 * 
 * 1. ✅ Make sure you're using ignoreExpiration = true (third parameter)
 * 2. ✅ Verify certificate has proper -----BEGIN CERTIFICATE----- headers
 * 3. ✅ Check that certificate variable is not null/undefined
 * 4. ✅ Ensure certificate is passed as a string, not an object
 * 5. ✅ Try copying the certificate directly into your script (avoid external files)
 * 6. ✅ Check K6 console output for detailed error messages
 * 
 * Working call:
 * const encrypted = await payloadEncryptionFactory(payload, certificate, true);
 *                                                                        ^^^^
 *                                                                        MUST BE TRUE!
 */