/**
 * Debug date parsing for your certificate
 */

import * as jsrsasign from 'jsrsasign';

const YOUR_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

function debugDateParsing() {
  console.log('🔍 Debug Date Parsing\n');

  try {
    const cert = new jsrsasign.X509();
    cert.readCertPEM(YOUR_CERTIFICATE);

    const notBefore = cert.getNotBefore();
    const notAfter = cert.getNotAfter();

    console.log('Raw certificate dates:');
    console.log('- notBefore (raw):', notBefore);
    console.log('- notAfter (raw):', notAfter);

    // Parse dates manually
    function parseX509Date(dateString: string): Date | undefined {
      try {
        if (!dateString || typeof dateString !== 'string') {
          return undefined;
        }

        // Remove 'Z' suffix if present
        const cleanDate = dateString.replace(/Z$/, '');
        console.log('- cleanDate:', cleanDate);

        let year: number;
        let month: number;
        let day: number;
        let hour: number;
        let minute: number;
        let second: number;

        if (cleanDate.length === 12) {
          // YYMMDDHHMMSS format
          const yy = parseInt(cleanDate.substring(0, 2), 10);
          // Convert 2-digit year to 4-digit (assume 1950-2049 range)
          year = yy >= 50 ? 1900 + yy : 2000 + yy;
          month = parseInt(cleanDate.substring(2, 4), 10);
          day = parseInt(cleanDate.substring(4, 6), 10);
          hour = parseInt(cleanDate.substring(6, 8), 10);
          minute = parseInt(cleanDate.substring(8, 10), 10);
          second = parseInt(cleanDate.substring(10, 12), 10);
        } else if (cleanDate.length === 14) {
          // YYYYMMDDHHMMSS format
          year = parseInt(cleanDate.substring(0, 4), 10);
          month = parseInt(cleanDate.substring(4, 6), 10);
          day = parseInt(cleanDate.substring(6, 8), 10);
          hour = parseInt(cleanDate.substring(8, 10), 10);
          minute = parseInt(cleanDate.substring(10, 12), 10);
          second = parseInt(cleanDate.substring(12, 14), 10);
        } else {
          return undefined;
        }

        console.log(`- Parsed components: ${year}-${month}-${day} ${hour}:${minute}:${second}`);

        // Create Date object (month is 0-indexed in JavaScript)
        return new Date(year, month - 1, day, hour, minute, second);
      } catch (error) {
        console.log('- Parse error:', error);
        return undefined;
      }
    }

    const parsedNotBefore = parseX509Date(notBefore);
    const parsedNotAfter = parseX509Date(notAfter);

    console.log('\nParsed dates:');
    console.log('- notBefore (parsed):', parsedNotBefore?.toISOString());
    console.log('- notAfter (parsed):', parsedNotAfter?.toISOString());

    const now = new Date();
    console.log('- Current time:', now.toISOString());

    if (parsedNotAfter) {
      const isExpired = parsedNotAfter < now;
      const daysUntilExpiry = Math.floor((parsedNotAfter.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      console.log('\nExpiration analysis:');
      console.log('- Is expired:', isExpired);
      console.log('- Days until expiry:', daysUntilExpiry);
      console.log('- Time difference (ms):', parsedNotAfter.getTime() - now.getTime());
    }

    // Test with different current dates
    console.log('\n🧪 Testing with different dates:');
    
    const testDates = [
      new Date('2024-12-01T00:00:00Z'), // Should be valid
      new Date('2025-05-01T00:00:00Z'), // Should be valid
      new Date('2025-05-24T00:00:00Z'), // Should be expired
      new Date('2025-09-10T00:00:00Z')  // Current date
    ];

    testDates.forEach(testDate => {
      if (parsedNotAfter) {
        const isExpired = parsedNotAfter < testDate;
        console.log(`- ${testDate.toISOString()}: ${isExpired ? 'EXPIRED' : 'VALID'}`);
      }
    });

  } catch (error) {
    console.error('Error parsing certificate:', error);
  }
}

debugDateParsing();