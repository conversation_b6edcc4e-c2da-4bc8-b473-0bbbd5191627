/**
 * Fix certificate format - add proper line breaks
 */

import { 
  payloadEncryptionFactoryWithErrors,
  validateCertificateInput,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate as it appears in the logs (all on one line - INVALID)
const INVALID_CERTIFICATE = `-----BEGIN CERTIFICATE-----MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQELBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ9wDQYDVQQLDAZNeVVuaXQxFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMyMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlVbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7VXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvRxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsIFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNNN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQNrrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWyAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvGSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/QmKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxBoI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzCwW5lyO+/qcFe-----END CERTIFICATE-----`;

// Your certificate with proper formatting (VALID)
const VALID_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Your payload
const PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

/**
 * Function to format certificate properly
 */
function formatCertificate(certString: string): string {
  // Remove all whitespace and line breaks
  let cleanCert = certString.replace(/\s/g, '');
  
  // Extract the content between BEGIN and END markers
  const beginMarker = '-----BEGINCERTIFICATE-----';
  const endMarker = '-----ENDCERTIFICATE-----';
  
  const beginIndex = cleanCert.indexOf(beginMarker);
  const endIndex = cleanCert.indexOf(endMarker);
  
  if (beginIndex === -1 || endIndex === -1) {
    throw new Error('Invalid certificate: missing BEGIN or END markers');
  }
  
  const certContent = cleanCert.substring(beginIndex + beginMarker.length, endIndex);
  
  // Split into 64-character lines
  const lines = [];
  for (let i = 0; i < certContent.length; i += 64) {
    lines.push(certContent.substring(i, i + 64));
  }
  
  // Reconstruct with proper formatting
  return `-----BEGIN CERTIFICATE-----\n${lines.join('\n')}\n-----END CERTIFICATE-----`;
}

async function testCertificateFormat() {
  console.log('🔧 Certificate Format Fix Test\n');

  // Test 1: Invalid certificate (all on one line)
  console.log('1. Testing INVALID certificate (all on one line):');
  const invalidValidation = validateCertificateInput(INVALID_CERTIFICATE);
  
  if (invalidValidation.success) {
    console.log('   ❌ Unexpected: Invalid certificate passed validation');
  } else {
    console.log('   ✅ Expected: Invalid certificate rejected');
    console.log(`   Error: ${invalidValidation.error?.message}`);
    if (invalidValidation.error?.details) {
      console.log(`   Details: ${JSON.stringify(invalidValidation.error.details)}`);
    }
  }

  console.log('');

  // Test 2: Valid certificate (properly formatted)
  console.log('2. Testing VALID certificate (properly formatted):');
  const validValidation = validateCertificateInput(VALID_CERTIFICATE);
  
  if (validValidation.success) {
    console.log('   ✅ Valid certificate accepted');
  } else {
    console.log('   ❌ Unexpected: Valid certificate rejected');
    console.log(`   Error: ${validValidation.error?.message}`);
  }

  console.log('');

  // Test 3: Auto-format the invalid certificate
  console.log('3. Auto-formatting the invalid certificate:');
  try {
    const formattedCert = formatCertificate(INVALID_CERTIFICATE);
    console.log('   ✅ Certificate formatted successfully');
    console.log('   First few lines:');
    console.log('   ' + formattedCert.split('\n').slice(0, 3).join('\n   '));
    console.log('   ...');
    
    // Test the formatted certificate
    const formattedValidation = validateCertificateInput(formattedCert);
    if (formattedValidation.success) {
      console.log('   ✅ Formatted certificate is valid');
    } else {
      console.log('   ❌ Formatted certificate still invalid');
    }
  } catch (error) {
    console.log(`   ❌ Formatting failed: ${error}`);
  }

  console.log('');

  // Test 4: Encryption with valid certificate
  console.log('4. Testing encryption with VALID certificate:');
  const result = await payloadEncryptionFactoryWithErrors(PAYLOAD, VALID_CERTIFICATE, true);
  
  if (result.success) {
    console.log('   🎉 SUCCESS! Encryption works with properly formatted certificate');
    console.log(`   Encrypted length: ${result.data?.length} characters`);
  } else {
    console.log('   ❌ Encryption failed');
    console.log(`   Error: ${formatEncryptionError(result.error!)}`);
  }

  console.log('\n📋 SOLUTION FOR YOUR K6 SCRIPT:');
  console.log('Replace your certificate variable with this properly formatted version:');
  console.log('');
  console.log('```javascript');
  console.log('const certificate = `-----BEGIN CERTIFICATE-----');
  console.log('MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL');
  console.log('BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM');
  console.log('DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx');
  console.log('FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy');
  console.log('MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD');
  console.log('VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV');
  console.log('bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB');
  console.log('DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V');
  console.log('XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR');
  console.log('2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR');
  console.log('xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI');
  console.log('FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN');
  console.log('N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN');
  console.log('rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy');
  console.log('AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG');
  console.log('SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ');
  console.log('I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm');
  console.log('KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb');
  console.log('oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3');
  console.log('YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC');
  console.log('wW5lyO+/qcFe');
  console.log('-----END CERTIFICATE-----`;');
  console.log('```');
}

testCertificateFormat().catch(console.error);