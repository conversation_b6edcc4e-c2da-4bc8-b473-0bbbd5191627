/**
 * Fix certificate parsing issue by ensuring proper formatting
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate as it appears in logs (still one line)
const CERTIFICATE_ONE_LINE = `-----B<PERSON>IN CERTIFICATE-----MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQELBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQxFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMyMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlVbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7VXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvRxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsIFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNNN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQNrrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWyAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvGSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/QmKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxBoI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzCwW5lyO+/qcFe-----END CERTIFICATE-----`;

// Your payload
const PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

/**
 * Force format certificate to proper PEM format for jsrsasign
 */
function forceFormatCertificate(certString: string): string {
  // Remove all whitespace and line breaks
  let cleanCert = certString.replace(/\s/g, '');
  
  // Extract content between markers
  const beginMarker = '-----BEGINCERTIFICATE-----';
  const endMarker = '-----ENDCERTIFICATE-----';
  
  const beginIndex = cleanCert.indexOf(beginMarker);
  const endIndex = cleanCert.indexOf(endMarker);
  
  if (beginIndex === -1 || endIndex === -1) {
    throw new Error('Certificate markers not found');
  }
  
  const certContent = cleanCert.substring(beginIndex + beginMarker.length, endIndex);
  
  // Split into 64-character lines
  const lines = [];
  for (let i = 0; i < certContent.length; i += 64) {
    lines.push(certContent.substring(i, i + 64));
  }
  
  // Return properly formatted certificate
  return `-----BEGIN CERTIFICATE-----\n${lines.join('\n')}\n-----END CERTIFICATE-----`;
}

async function testCertificateParsing() {
  console.log('🔧 Testing Certificate Parsing Fix\n');

  // Test 1: Original certificate (should fail parsing)
  console.log('1. Testing original certificate parsing:');
  try {
    const certInfo1 = parsePEMCertificate(CERTIFICATE_ONE_LINE, true);
    console.log(`   Certificate valid: ${certInfo1.valid}`);
    if (!certInfo1.valid) {
      console.log('   ❌ Certificate parsing failed (expected)');
    }
  } catch (error) {
    console.log(`   ❌ Certificate parsing exception: ${error}`);
  }

  console.log('');

  // Test 2: Force formatted certificate
  console.log('2. Testing force formatted certificate:');
  try {
    const formattedCert = forceFormatCertificate(CERTIFICATE_ONE_LINE);
    console.log('   ✅ Certificate formatted successfully');
    console.log('   First few lines:');
    console.log('   ' + formattedCert.split('\n').slice(0, 4).join('\n   '));
    console.log('   ...');
    
    // Test parsing
    const certInfo2 = parsePEMCertificate(formattedCert, true);
    console.log(`   Certificate valid: ${certInfo2.valid}`);
    console.log(`   Key size: ${certInfo2.keySize} bits`);
    console.log(`   Algorithm: ${certInfo2.algorithm}`);
    
    if (certInfo2.valid) {
      console.log('   ✅ Certificate parsing successful!');
    } else {
      console.log('   ❌ Certificate parsing still failed');
    }
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }

  console.log('');

  // Test 3: Full encryption with formatted certificate
  console.log('3. Testing encryption with formatted certificate:');
  try {
    const formattedCert = forceFormatCertificate(CERTIFICATE_ONE_LINE);
    const result = await payloadEncryptionFactoryWithErrors(PAYLOAD, formattedCert, true);
    
    if (result.success) {
      console.log('   🎉 SUCCESS! Encryption worked with formatted certificate!');
      console.log(`   Encrypted length: ${result.data?.length} characters`);
      console.log(`   First 50 chars: ${result.data?.substring(0, 50)}...`);
    } else {
      console.log('   ❌ Encryption failed');
      console.log(`   Error: ${formatEncryptionError(result.error!)}`);
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('\n🎯 SOLUTION FOR YOUR K6 SCRIPT:');
  console.log('Add this function to your K6 script and use it to format the certificate:');
  console.log('');
  console.log('```javascript');
  console.log('function forceFormatCertificate(certString) {');
  console.log('  const cleanCert = certString.replace(/\\s/g, "");');
  console.log('  const beginMarker = "-----BEGINCERTIFICATE-----";');
  console.log('  const endMarker = "-----ENDCERTIFICATE-----";');
  console.log('  const beginIndex = cleanCert.indexOf(beginMarker);');
  console.log('  const endIndex = cleanCert.indexOf(endMarker);');
  console.log('  const certContent = cleanCert.substring(beginIndex + beginMarker.length, endIndex);');
  console.log('  const lines = [];');
  console.log('  for (let i = 0; i < certContent.length; i += 64) {');
  console.log('    lines.push(certContent.substring(i, i + 64));');
  console.log('  }');
  console.log('  return `-----BEGIN CERTIFICATE-----\\n${lines.join("\\n")}\\n-----END CERTIFICATE-----`;');
  console.log('}');
  console.log('');
  console.log('// In your postResetPinOtp function:');
  console.log('const formattedCertificate = forceFormatCertificate(key);');
  console.log('const encryptedPayload = await payloadEncryptionFactory(body, formattedCertificate, true);');
  console.log('```');
}

testCertificateParsing().catch(console.error);