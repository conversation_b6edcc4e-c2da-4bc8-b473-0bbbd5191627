/**
 * Test aggressive bypass for certificate parsing
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate as it appears in logs (one line)
const CERTIFICATE_ONE_LINE = `-----B<PERSON>IN CERTIFICATE-----MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQELBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQxFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMyMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlVbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7VXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvRxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsIFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNNN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQNrrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWyAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvGSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/QmKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxBoI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzCwW5lyO+/qcFe-----END CERTIFICATE-----`;

// Properly formatted version
const CERTIFICATE_FORMATTED = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Your payload
const PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

async function testAggressiveBypass() {
  console.log('🔧 Testing Aggressive Certificate Parsing Bypass\n');

  // Test 1: One line certificate with ignoreExpiration = true
  console.log('1. Testing one-line certificate with ignoreExpiration = true:');
  try {
    const certInfo1 = parsePEMCertificate(CERTIFICATE_ONE_LINE, true);
    console.log(`   Certificate valid: ${certInfo1.valid}`);
    console.log(`   Has public key: ${certInfo1.publicKey ? 'YES' : 'NO'}`);
    console.log(`   Key size: ${certInfo1.keySize}`);
    console.log(`   Algorithm: ${certInfo1.algorithm}`);
    
    if (certInfo1.valid && certInfo1.publicKey) {
      console.log('   ✅ Certificate parsing successful with bypass!');
    } else {
      console.log('   ❌ Certificate parsing still failed');
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('');

  // Test 2: Properly formatted certificate
  console.log('2. Testing properly formatted certificate:');
  try {
    const certInfo2 = parsePEMCertificate(CERTIFICATE_FORMATTED, true);
    console.log(`   Certificate valid: ${certInfo2.valid}`);
    console.log(`   Has public key: ${certInfo2.publicKey ? 'YES' : 'NO'}`);
    console.log(`   Key size: ${certInfo2.keySize}`);
    
    if (certInfo2.valid && certInfo2.publicKey) {
      console.log('   ✅ Formatted certificate parsing successful!');
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('');

  // Test 3: Full encryption with one-line certificate
  console.log('3. Testing encryption with one-line certificate (ignoreExpiration = true):');
  const result1 = await payloadEncryptionFactoryWithErrors(PAYLOAD, CERTIFICATE_ONE_LINE, true);
  
  if (result1.success) {
    console.log('   🎉 SUCCESS! One-line certificate encryption worked!');
    console.log(`   Encrypted length: ${result1.data?.length} characters`);
  } else {
    console.log('   ❌ One-line certificate encryption failed');
    console.log(`   Error: ${formatEncryptionError(result1.error!)}`);
  }

  console.log('');

  // Test 4: Full encryption with formatted certificate
  console.log('4. Testing encryption with formatted certificate:');
  const result2 = await payloadEncryptionFactoryWithErrors(PAYLOAD, CERTIFICATE_FORMATTED, true);
  
  if (result2.success) {
    console.log('   ✅ Formatted certificate encryption successful!');
    console.log(`   Encrypted length: ${result2.data?.length} characters`);
  } else {
    console.log('   ❌ Formatted certificate encryption failed');
    console.log(`   Error: ${formatEncryptionError(result2.error!)}`);
  }

  console.log('\n🎯 RECOMMENDATION:');
  if (result1.success) {
    console.log('✅ Your one-line certificate now works with ignoreExpiration = true');
    console.log('✅ No need to format the certificate manually');
    console.log('🚀 Just use: payloadEncryptionFactory(payload, certificate, true)');
  } else if (result2.success) {
    console.log('✅ Certificate works when properly formatted');
    console.log('🔧 You need to format the certificate before using it');
    console.log('🚀 Use the forceFormatCertificate function in your K6 script');
  } else {
    console.log('❌ Still having issues - need to investigate jsrsasign compatibility');
  }
}

testAggressiveBypass().catch(console.error);