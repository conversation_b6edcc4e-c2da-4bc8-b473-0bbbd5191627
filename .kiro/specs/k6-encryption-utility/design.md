# Design Document

## Overview

The K6 Encryption Utility provides PKCS#7-compatible encryption functionality for K6 load testing scripts. Since K6 v1.1.1 doesn't support native PKCS#7 operations or Node.js-specific libraries like `node-forge`, this utility uses the `jsrsasign` library (already in dependencies) to implement RSA encryption with OAEP padding, which provides similar security properties to PKCS#7 enveloped data.

The utility will encrypt JSON payloads using X.509 certificates and return base64-encoded results compatible with systems expecting PKCS#7 format.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   K6 Script     │───▶│  Encryption      │───▶│  Encrypted      │
│   (JSON Payload)│    │  Utility         │    │  Base64 Output  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  X.509 Cert     │
                       │  Parser          │
                       └──────────────────┘
```

### Component Interaction Flow

1. **Input Processing**: Accept JSON payload and PEM certificate
2. **Certificate Parsing**: Extract public key from X.509 certificate
3. **Payload Preparation**: Serialize JSON to string format
4. **Encryption**: Apply RSA-OAEP encryption to the payload
5. **Output Formatting**: Encode result as base64 string

## Components and Interfaces

### Core Encryption Module (`src/utils/encryption.ts`)

```typescript
interface EncryptionResult {
  success: boolean;
  data?: string;
  error?: string;
}

interface CertificateInfo {
  publicKey: any;
  valid: boolean;
}

// Main encryption function
export function payloadEncryptionFactory(
  payload: any, 
  certificate: string
): Promise<string | null>

// Certificate parsing function
export function parsePEMCertificate(
  pemCertificate: string
): CertificateInfo

// RSA encryption with OAEP padding
function encryptWithRSAOAEP(
  message: string, 
  publicKey: any
): string
```

### Utility Functions

```typescript
// Base64 encoding compatible with K6
function encodeToBase64(data: Uint8Array): string

// String to Uint8Array conversion
function stringToUint8Array(str: string): Uint8Array

// Certificate validation
function validateCertificateFormat(pemCert: string): boolean
```

## Data Models

### Input Data Model

```typescript
interface EncryptionInput {
  payload: {
    pin: string;
    cardId: string;
    cardType: string;
    otp: string;
  };
  certificate: string; // PEM format X.509 certificate
}
```

### Output Data Model

```typescript
interface EncryptionOutput {
  encryptedData: string; // Base64-encoded encrypted payload
}
```

### Certificate Data Model

```typescript
interface X509Certificate {
  version: number;
  serialNumber: string;
  issuer: string;
  subject: string;
  publicKey: {
    algorithm: string;
    keySize: number;
    exponent: string;
    modulus: string;
  };
  validFrom: Date;
  validTo: Date;
}
```

## Error Handling

### Error Categories

1. **Certificate Errors**
   - Invalid PEM format
   - Expired certificate
   - Unsupported key algorithm
   - Missing public key

2. **Encryption Errors**
   - Payload too large for key size
   - Invalid key parameters
   - Encryption operation failure

3. **Input Validation Errors**
   - Missing required parameters
   - Invalid payload format
   - Empty certificate string

### Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}
```

### Error Handling Strategy

- Use try-catch blocks around all cryptographic operations
- Return null for encryption failures (matching original API)
- Log detailed errors for debugging while returning simple messages
- Validate inputs before processing to fail fast

## Testing Strategy

### Unit Tests

1. **Certificate Parsing Tests**
   - Valid PEM certificate parsing
   - Invalid certificate format handling
   - Expired certificate detection
   - Different key sizes (1024, 2048, 4096 bits)

2. **Encryption Function Tests**
   - Successful encryption with valid inputs
   - Payload size limit testing
   - Different payload structures
   - Error handling for invalid certificates

3. **Utility Function Tests**
   - Base64 encoding accuracy
   - String to Uint8Array conversion
   - Certificate format validation

### Integration Tests

1. **End-to-End Encryption Tests**
   - Complete workflow from JSON input to base64 output
   - Compatibility with expected output format
   - Performance benchmarking

2. **K6 Compatibility Tests**
   - Import and usage in K6 scripts
   - Memory usage validation
   - Concurrent encryption operations

### Test Data

- Sample X.509 certificates (RSA 2048-bit)
- Various JSON payload structures
- Expected encrypted outputs for validation
- Invalid certificate formats for error testing

## Implementation Approach

### Phase 1: Core Encryption Infrastructure
- Set up jsrsasign integration
- Implement certificate parsing
- Create basic RSA encryption function

### Phase 2: K6 Compatibility Layer
- Implement K6-compatible base64 encoding
- Handle Uint8Array conversions
- Ensure no Node.js dependencies

### Phase 3: API Integration
- Create main encryption factory function
- Implement error handling
- Add input validation

### Phase 4: Testing and Validation
- Unit test coverage
- Integration testing
- Performance validation

## Security Considerations

1. **Key Management**
   - Certificates should be validated before use
   - Support for standard RSA key sizes (2048+ bits)
   - Proper handling of certificate expiration

2. **Encryption Standards**
   - Use RSA-OAEP padding for security
   - Ensure random padding for each encryption
   - Validate payload size against key constraints

3. **Data Handling**
   - Clear sensitive data from memory when possible
   - Avoid logging sensitive payload content
   - Validate input sanitization

## Performance Considerations

1. **Optimization Strategies**
   - Cache parsed certificates when possible
   - Minimize string conversions
   - Use efficient base64 encoding

2. **Resource Management**
   - Monitor memory usage during encryption
   - Optimize for K6's JavaScript engine
   - Consider payload size limitations

3. **Scalability**
   - Design for concurrent encryption operations
   - Minimize CPU-intensive operations
   - Efficient error handling to avoid performance degradation