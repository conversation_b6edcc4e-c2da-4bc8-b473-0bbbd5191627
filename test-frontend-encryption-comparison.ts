/**
 * Test to compare different encryption approaches
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors 
} from './src/utils/encryption';

import { payloadEncryptionFactoryHybrid } from './src/utils/hybrid-encryption';

// Your certificate
const CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

const TEST_PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

async function compareEncryptionMethods() {
  console.log('🔍 Comparing Encryption Methods\n');

  // Method 1: Original RSA encryption (working but backend can't decrypt)
  console.log('1. Testing Original RSA Encryption:');
  const result1 = await payloadEncryptionFactory(TEST_PAYLOAD, CERTIFICATE, true);
  if (result1) {
    console.log('   ✅ Success');
    console.log('   Length:', result1.length);
    console.log('   First 50 chars:', result1.substring(0, 50));
    console.log('   Type: RSA-OAEP direct encryption');
  } else {
    console.log('   ❌ Failed');
  }

  console.log('\n2. Testing Hybrid Encryption (AES + RSA):');
  try {
    const result2 = await payloadEncryptionFactoryHybrid(TEST_PAYLOAD, CERTIFICATE);
    if (result2) {
      console.log('   ✅ Success');
      console.log('   Length:', result2.length);
      console.log('   First 50 chars:', result2.substring(0, 50));
      console.log('   Type: Hybrid AES-256-CBC + RSA key encryption');
    } else {
      console.log('   ❌ Failed');
    }
  } catch (error) {
    console.log('   ❌ Error:', error);
  }

  console.log('\n📊 Analysis:');
  console.log('- Original method: Direct RSA encryption of JSON payload');
  console.log('- Hybrid method: AES-256-CBC for data + RSA for key (similar to frontend)');
  console.log('- Backend expects: PKCS#7 format (same as frontend)');
  
  console.log('\n💡 Recommendation:');
  console.log('Use the hybrid method to match frontend encryption pattern');
}

compareEncryptionMethods().catch(console.error);