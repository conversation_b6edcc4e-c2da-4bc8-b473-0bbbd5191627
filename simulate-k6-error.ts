/**
 * Simulate the exact K6 error you're experiencing
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  formatEncryptionError
} from './src/utils/encryption';

// Test different scenarios that could cause CERTIFICATE_PARSING_FAILED
const TEST_SCENARIOS = [
  {
    name: "Your Certificate with ignoreExpiration = false",
    certificate: `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`,
    ignoreExpiration: false
  },
  {
    name: "Corrupted Certificate (missing characters)",
    certificate: `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/MISSING_CHARS
-----END CERTIFICATE-----`,
    ignoreExpiration: true
  },
  {
    name: "Empty Certificate",
    certificate: "",
    ignoreExpiration: true
  },
  {
    name: "Invalid Certificate Format",
    certificate: "not-a-certificate",
    ignoreExpiration: true
  },
  {
    name: "Certificate with Wrong Headers",
    certificate: `-----BEGIN RSA PRIVATE KEY-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
-----END RSA PRIVATE KEY-----`,
    ignoreExpiration: true
  }
];

const TEST_PAYLOAD = {
  "pin": "1234",
  "cardId": "test123",
  "cardType": "DebitCard",
  "otp": "123456"
};

async function simulateK6Error() {
  console.log('🎯 Simulating K6 Certificate Parsing Errors\n');

  for (const scenario of TEST_SCENARIOS) {
    console.log(`\n📋 Testing: ${scenario.name}`);
    console.log(`   Certificate length: ${scenario.certificate.length}`);
    console.log(`   Ignore expiration: ${scenario.ignoreExpiration}`);

    // Test certificate parsing directly
    console.log('\n   Direct Certificate Parsing:');
    try {
      const certInfo = parsePEMCertificate(scenario.certificate, scenario.ignoreExpiration);
      console.log(`      Valid: ${certInfo.valid}`);
      console.log(`      Public Key: ${certInfo.publicKey ? 'Present' : 'NULL'}`);
      console.log(`      Is Expired: ${certInfo.isExpired}`);
    } catch (error) {
      console.log(`      Parsing Exception: ${error}`);
    }

    // Test full encryption
    console.log('\n   Full Encryption Test:');
    try {
      const result = await payloadEncryptionFactoryWithErrors(
        TEST_PAYLOAD, 
        scenario.certificate, 
        scenario.ignoreExpiration
      );
      
      if (result.success) {
        console.log(`      ✅ SUCCESS`);
      } else {
        console.log(`      ❌ FAILED: ${formatEncryptionError(result.error!)}`);
        
        // Check if this matches your error
        if (result.error?.code === 'CERTIFICATE_PARSING_FAILED') {
          console.log(`      🎯 THIS MATCHES YOUR ERROR!`);
          console.log(`      Details: ${JSON.stringify(result.error.details, null, 2)}`);
        }
      }
    } catch (error) {
      console.log(`      Exception: ${error}`);
    }

    console.log('\n' + '='.repeat(60));
  }

  // Test the exact scenario that should work
  console.log('\n🚀 Testing the WORKING scenario:\n');
  
  const workingResult = await payloadEncryptionFactoryWithErrors(
    TEST_PAYLOAD,
    TEST_SCENARIOS[0].certificate, // Your certificate
    true // ignoreExpiration = true
  );

  console.log('Working scenario result:');
  if (workingResult.success) {
    console.log('✅ SUCCESS! This should work in K6');
    console.log(`Encrypted length: ${workingResult.data?.length}`);
  } else {
    console.log('❌ Even the working scenario failed:');
    console.log(formatEncryptionError(workingResult.error!));
  }

  // Show the exact K6 code that should work
  console.log('\n📝 Your K6 code should be:');
  console.log('```javascript');
  console.log('const encryptedPayload = await payloadEncryptionFactory(body, key, true);');
  console.log('//                                                              ^^^^');
  console.log('//                                                              This must be true!');
  console.log('```');
}

simulateK6Error().catch(console.error);