#!/bin/bash

# Integration Test Script for K6 Encryption Utility
# This script runs the integration tests using K6

echo "=== K6 Encryption Utility Integration Tests ==="
echo "Building project..."

# Build the project
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful"
echo ""

# Check if k6 is installed
if ! command -v k6 &> /dev/null; then
    echo "❌ K6 is not installed. Please install K6 to run integration tests."
    echo "Visit: https://k6.io/docs/getting-started/installation/"
    exit 1
fi

echo "✅ K6 is available"
echo ""

# Run different test scenarios
echo "=== Running Integration Tests ==="

echo "1. Running K6 Compatibility Test..."
k6 run --quiet dist/k6-compatibility-test.js

echo ""
echo "2. Running Integration Test (End-to-End)..."
k6 run --quiet -e K6_SCENARIO=end_to_end dist/integration-test-encryption.js

echo ""
echo "3. Running Performance Test (Short)..."
k6 run --quiet -e K6_SCENARIO=performance --duration 10s --vus 3 dist/performance-test-encryption.js

echo ""
echo "4. Running Unit Tests..."
k6 run --quiet dist/test-encryption-functionality.js

echo ""
echo "=== Integration Tests Complete ==="
echo "All tests have been executed. Check the output above for results."