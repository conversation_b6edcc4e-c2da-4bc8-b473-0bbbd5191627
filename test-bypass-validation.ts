/**
 * Test bypassing certificate format validation
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate as it appears in logs (all on one line - INVALID FORMAT)
const MALFORMED_CERTIFICATE = `-----B<PERSON>IN CERTIFICATE-----MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQELBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQxFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMyMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlVbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7VXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvRxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsIFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNNN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQNrrKJWgyJdZTqsAdpYOWyAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvGSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/QmKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxBoI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzCwW5lyO+/qcFe-----END CERTIFICATE-----`;

// Your payload
const PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

async function testBypassValidation() {
  console.log('🔧 Testing Bypass Certificate Format Validation\n');

  // Test 1: Normal encryption (should fail due to format)
  console.log('1. Testing normal encryption (ignoreExpiration = false):');
  const result1 = await payloadEncryptionFactoryWithErrors(PAYLOAD, MALFORMED_CERTIFICATE, false);
  
  if (result1.success) {
    console.log('   ❌ Unexpected success');
  } else {
    console.log('   ✅ Expected failure');
    console.log(`   Error: ${result1.error?.code} - ${result1.error?.message}`);
  }

  console.log('');

  // Test 2: Encryption with ignoreExpiration = true (should now bypass format validation)
  console.log('2. Testing encryption with ignoreExpiration = true (should bypass format validation):');
  const result2 = await payloadEncryptionFactoryWithErrors(PAYLOAD, MALFORMED_CERTIFICATE, true);
  
  if (result2.success) {
    console.log('   🎉 SUCCESS! Format validation bypassed and encryption worked!');
    console.log(`   Encrypted length: ${result2.data?.length} characters`);
    console.log(`   First 50 chars: ${result2.data?.substring(0, 50)}...`);
  } else {
    console.log('   ❌ Still failed');
    console.log(`   Error: ${formatEncryptionError(result2.error!)}`);
  }

  console.log('');

  // Test 3: Basic function
  console.log('3. Testing basic payloadEncryptionFactory with ignoreExpiration = true:');
  const result3 = await payloadEncryptionFactory(PAYLOAD, MALFORMED_CERTIFICATE, true);
  
  if (result3) {
    console.log('   ✅ Basic function also works!');
    console.log(`   Length: ${result3.length} characters`);
  } else {
    console.log('   ❌ Basic function failed');
  }

  console.log('\n🎯 Summary:');
  if (result2.success || result3) {
    console.log('✅ SUCCESS! Certificate format validation bypass is working');
    console.log('✅ Your malformed certificate now works with ignoreExpiration = true');
    console.log('✅ No need to format the certificate manually');
    console.log('\n🚀 Your K6 script should now work with:');
    console.log('const encrypted = await payloadEncryptionFactory(payload, certificate, true);');
  } else {
    console.log('❌ Still having issues - need to investigate further');
  }
}

testBypassValidation().catch(console.error);