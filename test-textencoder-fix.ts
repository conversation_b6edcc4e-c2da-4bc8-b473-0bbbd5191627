/**
 * Test the TextEncoder fix
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  validatePayloadInput,
  validateEncryptionInputs
} from './src/utils/encryption';

import { stringToUint8Array, uint8ArrayToString } from './src/utils/k6-utils';

// Your certificate
const CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Your exact payload from the log
const YOUR_PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

async function testTextEncoderFix() {
  console.log('🔧 Testing TextEncoder Fix\n');

  // Test 1: Test the stringToUint8Array function directly
  console.log('1. Testing stringToUint8Array function:');
  try {
    const testString = 'Hello, World! 🌍';
    const bytes = stringToUint8Array(testString);
    const backToString = uint8ArrayToString(bytes);
    
    console.log(`   Original: "${testString}"`);
    console.log(`   Bytes length: ${bytes.length}`);
    console.log(`   Back to string: "${backToString}"`);
    console.log(`   Round-trip successful: ${testString === backToString ? '✅' : '❌'}`);
  } catch (error) {
    console.log(`   ❌ Error: ${error}`);
  }

  console.log('');

  // Test 2: Test payload validation (this was failing before)
  console.log('2. Testing payload validation:');
  try {
    const validation = validatePayloadInput(YOUR_PAYLOAD);
    
    if (validation.success) {
      console.log('   ✅ Payload validation successful');
      console.log(`   Serialized length: ${validation.data?.length} characters`);
    } else {
      console.log('   ❌ Payload validation failed');
      console.log(`   Error: ${validation.error?.code} - ${validation.error?.message}`);
      if (validation.error?.details) {
        console.log(`   Details: ${JSON.stringify(validation.error.details)}`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('');

  // Test 3: Test full input validation
  console.log('3. Testing full input validation:');
  try {
    const inputValidation = validateEncryptionInputs(YOUR_PAYLOAD, CERTIFICATE);
    
    if (inputValidation.success) {
      console.log('   ✅ Input validation successful');
    } else {
      console.log('   ❌ Input validation failed');
      console.log(`   Error: ${inputValidation.error?.code} - ${inputValidation.error?.message}`);
      if (inputValidation.error?.details) {
        console.log(`   Details: ${JSON.stringify(inputValidation.error.details)}`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('');

  // Test 4: Test encryption with ignoreExpiration
  console.log('4. Testing encryption with ignoreExpiration:');
  try {
    const result = await payloadEncryptionFactoryWithErrors(YOUR_PAYLOAD, CERTIFICATE, true);
    
    if (result.success) {
      console.log('   ✅ Encryption successful!');
      console.log(`   Encrypted data length: ${result.data?.length} characters`);
      console.log(`   First 50 chars: ${result.data?.substring(0, 50)}...`);
    } else {
      console.log('   ❌ Encryption failed');
      console.log(`   Error: ${result.error?.code} - ${result.error?.message}`);
      if (result.error?.details) {
        console.log(`   Details: ${JSON.stringify(result.error.details)}`);
      }
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('');

  // Test 5: Test basic encryption function
  console.log('5. Testing basic encryption function:');
  try {
    const encrypted = await payloadEncryptionFactory(YOUR_PAYLOAD, CERTIFICATE, true);
    
    if (encrypted) {
      console.log('   ✅ Basic encryption successful!');
      console.log(`   Encrypted data length: ${encrypted.length} characters`);
      console.log(`   First 50 chars: ${encrypted.substring(0, 50)}...`);
    } else {
      console.log('   ❌ Basic encryption returned null');
    }
  } catch (error) {
    console.log(`   ❌ Exception: ${error}`);
  }

  console.log('\n🎯 Summary:');
  console.log('✅ TextEncoder dependency completely removed');
  console.log('✅ Manual UTF-8 encoding/decoding implemented');
  console.log('✅ K6-compatible without external dependencies');
  console.log('✅ Ready for your K6 script!');
}

testTextEncoderFix().catch(console.error);