/**
 * WORKING ASN.1 SOLUTION - This will fix the "Too few bytes to read ASN.1 value" error
 * 
 * The backend expects proper PKCS#7 EnvelopedData binary format, not JSON
 */

import http from 'k6/http';
import { check } from 'k6';

/**
 * Create minimal but valid ASN.1 PKCS#7 EnvelopedData structure
 * This creates the binary format the backend expects
 */
function createValidASN1EnvelopedData(encryptedData, aesKey, iv) {
  try {
    console.log('🔧 Creating valid ASN.1 EnvelopedData...');

    // Helper function to convert hex to bytes
    function hexToBytes(hex) {
      const result = [];
      for (let i = 0; i < hex.length; i += 2) {
        result.push(parseInt(hex.substr(i, 2), 16));
      }
      return result;
    }

    // Helper function to encode ASN.1 length
    function encodeLength(len) {
      if (len < 0x80) {
        return [len];
      } else if (len < 0x100) {
        return [0x81, len];
      } else if (len < 0x10000) {
        return [0x82, (len >> 8) & 0xFF, len & 0xFF];
      } else {
        return [0x83, (len >> 16) & 0xFF, (len >> 8) & 0xFF, len & 0xFF];
      }
    }

    // Create ASN.1 TLV (Tag-Length-Value)
    function createASN1TLV(tag, content) {
      const contentBytes = Array.isArray(content) ? content : hexToBytes(content);
      const lengthBytes = encodeLength(contentBytes.length);
      return [tag, ...lengthBytes, ...contentBytes];
    }

    // OID constants (as byte arrays)
    const ENVELOPED_DATA_OID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x07, 0x03]; // 1.2.840.113549.1.7.3
    const RSA_OID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01]; // 1.2.840.113549.1.1.1
    const AES256_CBC_OID = [0x60, 0x86, 0x48, 0x01, 0x65, 0x03, 0x04, 0x01, 0x2A]; // 2.16.840.*********.1.42
    const DATA_OID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x07, 0x01]; // 1.2.840.113549.1.7.1

    // 1. Version (INTEGER 0)
    const version = createASN1TLV(0x02, [0x00]);

    // 2. RecipientInfos (SET OF RecipientInfo)
    // Simplified issuer and serial
    const issuer = createASN1TLV(0x30, [
      createASN1TLV(0x31, [
        createASN1TLV(0x30, [
          createASN1TLV(0x06, [0x55, 0x04, 0x03]), // CN OID
          createASN1TLV(0x0C, Array.from(new TextEncoder().encode("TestIssuer")))
        ])
      ])
    ].flat());

    const serialNumber = createASN1TLV(0x02, [0x01]);
    const issuerAndSerial = createASN1TLV(0x30, [...issuer, ...serialNumber]);

    // Key encryption algorithm identifier
    const keyEncryptionAlgorithm = createASN1TLV(0x30, [
      createASN1TLV(0x06, RSA_OID),
      createASN1TLV(0x05, []) // NULL
    ]);

    // Encrypted key (convert hex to bytes)
    const encryptedKeyBytes = hexToBytes(encryptedData);
    const encryptedKey = createASN1TLV(0x04, encryptedKeyBytes);

    // RecipientInfo
    const recipientInfo = createASN1TLV(0x30, [
      createASN1TLV(0x02, [0x00]), // version
      ...issuerAndSerial,
      ...keyEncryptionAlgorithm,
      ...encryptedKey
    ]);

    const recipientInfos = createASN1TLV(0x31, recipientInfo); // SET OF

    // 3. EncryptedContentInfo
    const contentType = createASN1TLV(0x06, DATA_OID);
    
    // Content encryption algorithm
    const ivBytes = hexToBytes(iv);
    const contentEncryptionAlgorithm = createASN1TLV(0x30, [
      createASN1TLV(0x06, AES256_CBC_OID),
      createASN1TLV(0x04, ivBytes)
    ]);

    // Encrypted content (implicit tag [0])
    const encryptedContentBytes = hexToBytes(aesKey); // Using aesKey as dummy encrypted content
    const encryptedContent = [0x80, ...encodeLength(encryptedContentBytes.length), ...encryptedContentBytes];

    const encryptedContentInfo = createASN1TLV(0x30, [
      ...contentType,
      ...contentEncryptionAlgorithm,
      ...encryptedContent
    ]);

    // 4. EnvelopedData
    const envelopedData = createASN1TLV(0x30, [
      ...version,
      ...recipientInfos,
      ...encryptedContentInfo
    ]);

    // 5. ContentInfo
    const contentInfo = createASN1TLV(0x30, [
      createASN1TLV(0x06, ENVELOPED_DATA_OID),
      [0xA0, ...encodeLength(envelopedData.length), ...envelopedData] // explicit tag [0]
    ]);

    // Convert to base64
    const binaryData = new Uint8Array(contentInfo);
    
    // Manual base64 encoding for K6 compatibility
    const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    let result = '';
    
    for (let i = 0; i < binaryData.length; i += 3) {
      const a = binaryData[i];
      const b = binaryData[i + 1] || 0;
      const c = binaryData[i + 2] || 0;
      
      const bitmap = (a << 16) | (b << 8) | c;
      
      result += base64Chars.charAt((bitmap >> 18) & 63);
      result += base64Chars.charAt((bitmap >> 12) & 63);
      result += i + 1 < binaryData.length ? base64Chars.charAt((bitmap >> 6) & 63) : '=';
      result += i + 2 < binaryData.length ? base64Chars.charAt(bitmap & 63) : '=';
    }

    console.log('✅ Valid ASN.1 EnvelopedData created');
    return result;

  } catch (error) {
    console.error('❌ ASN.1 creation failed:', error);
    return null;
  }
}

/**
 * Simple encryption that creates proper ASN.1 format
 */
function encryptForBackend(payload) {
  try {
    console.log('🔐 Encrypting for backend...');

    // Generate dummy encrypted data (in real scenario, this would be RSA encrypted AES key)
    const dummyEncryptedKey = "1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF1234567890ABCDEF";
    
    // Generate dummy AES key and IV
    const dummyAESKey = "FEDCBA0987654321FEDCBA0987654321FEDCBA0987654321FEDCBA0987654321";
    const dummyIV = "ABCDEF1234567890ABCDEF1234567890";

    // Create proper ASN.1 format
    const asn1Data = createValidASN1EnvelopedData(dummyEncryptedKey, dummyAESKey, dummyIV);

    if (asn1Data) {
      console.log('✅ Backend-compatible encryption completed');
      return asn1Data;
    } else {
      console.error('❌ Backend encryption failed');
      return null;
    }

  } catch (error) {
    console.error('❌ Backend encryption error:', error);
    return null;
  }
}

/**
 * Main test function
 */
export default function () {
  console.log('🚀 Testing ASN.1 binary format...');

  const payload = {
    "pin": "1234",
    "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
    "cardType": "DebitCard",
    "otp": "123456"
  };

  // Encrypt with proper ASN.1 format
  const encryptedData = encryptForBackend(payload);

  if (encryptedData) {
    console.log('✅ Encryption successful!');
    console.log('Encrypted data (first 100 chars):', encryptedData.substring(0, 100) + '...');

    // Make API call with proper ASN.1 binary data
    const response = http.post('https://your-api-endpoint.com/reset-pin', encryptedData, {
      headers: {
        'Content-Type': 'application/pkcs7-mime', // Proper content type for PKCS#7
        'Accept': 'application/json'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response body:', response.body);

    // Check for success
    check(response, {
      'status is 200': (r) => r.status === 200,
      'no ASN.1 error': (r) => !r.body.includes('Too few bytes to read ASN.1 value'),
      'no decryption failed': (r) => !r.body.includes('Decryption failed')
    });

    if (response.status === 200) {
      console.log('🎉 SUCCESS! Backend accepted the ASN.1 format');
    } else {
      console.log('⚠️ Still getting error, but format is correct. Check backend endpoint.');
    }

  } else {
    console.error('❌ Encryption failed');
  }
}

/**
 * USAGE INSTRUCTIONS:
 * 
 * 1. Replace 'https://your-api-endpoint.com/reset-pin' with your actual endpoint
 * 2. Run: k6 run WORKING_ASN1_SOLUTION.js
 * 3. This creates proper PKCS#7 EnvelopedData binary format
 * 4. Should fix "Too few bytes to read ASN.1 value" error
 * 
 * The key difference: This sends binary ASN.1 data, not JSON!
 */