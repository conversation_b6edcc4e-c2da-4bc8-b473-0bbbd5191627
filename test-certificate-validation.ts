/**
 * Test certificate validation with your specific certificate
 */

import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate
const YOUR_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Test payload
const TEST_PAYLOAD = {
  "pin": "1234",
  "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
  "cardType": "DebitCard",
  "otp": "123456"
};

async function testCertificateValidation() {
  console.log('🔍 Testing Certificate Validation\n');

  // Test 1: Parse certificate directly
  console.log('1. Testing certificate parsing (ignoreExpiration = false):');
  const certInfo1 = parsePEMCertificate(YOUR_CERTIFICATE, false);
  console.log('   Certificate Info:', {
    valid: certInfo1.valid,
    keySize: certInfo1.keySize,
    algorithm: certInfo1.algorithm,
    isExpired: certInfo1.isExpired,
    expirationDate: certInfo1.expirationDate,
    issuer: certInfo1.issuer,
    subject: certInfo1.subject
  });

  console.log('\n2. Testing certificate parsing (ignoreExpiration = true):');
  const certInfo2 = parsePEMCertificate(YOUR_CERTIFICATE, true);
  console.log('   Certificate Info:', {
    valid: certInfo2.valid,
    keySize: certInfo2.keySize,
    algorithm: certInfo2.algorithm,
    isExpired: certInfo2.isExpired,
    expirationDate: certInfo2.expirationDate,
    issuer: certInfo2.issuer,
    subject: certInfo2.subject
  });

  // Test 3: Full encryption with detailed errors
  console.log('\n3. Testing full encryption (ignoreExpiration = false):');
  const result1 = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, YOUR_CERTIFICATE, false);
  if (result1.success) {
    console.log('   ✅ Success!');
  } else {
    console.log('   ❌ Failed:', formatEncryptionError(result1.error!));
  }

  console.log('\n4. Testing full encryption (ignoreExpiration = true):');
  const result2 = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, YOUR_CERTIFICATE, true);
  if (result2.success) {
    console.log('   ✅ Success!');
    console.log('   Encrypted length:', result2.data?.length);
  } else {
    console.log('   ❌ Failed:', formatEncryptionError(result2.error!));
  }

  // Test 5: Basic function
  console.log('\n5. Testing basic payloadEncryptionFactory (ignoreExpiration = true):');
  const result3 = await payloadEncryptionFactory(TEST_PAYLOAD, YOUR_CERTIFICATE, true);
  if (result3) {
    console.log('   ✅ Basic function works!');
    console.log('   Length:', result3.length);
  } else {
    console.log('   ❌ Basic function failed');
  }

  console.log('\n📊 Summary:');
  console.log('Certificate Details from Online Decoder:');
  console.log('- Common Name: mydomain.com');
  console.log('- Valid From: May 23, 2024');
  console.log('- Valid To: May 23, 2025');
  console.log('- Organization: MyOrg');
  console.log('- Current Date:', new Date().toISOString());
  
  const now = new Date();
  const validFrom = new Date('2024-05-23T20:43:35Z');
  const validTo = new Date('2025-05-23T20:43:35Z');
  
  console.log('- Certificate should be valid:', now >= validFrom && now <= validTo);
}

testCertificateValidation().catch(console.error);