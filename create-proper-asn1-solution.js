/**
 * PROPER ASN.1 BINARY FORMAT SOLUTION
 * This creates the exact binary format the backend expects
 */

import { payloadEncryptionFactory } from "./dist/utils/encryption.js";
import http from 'k6/http';
import { check } from 'k6';

/**
 * Create proper ASN.1 binary format using raw bytes
 * This bypasses jsrsasign ASN.1 issues and creates the binary format manually
 */
function createASN1Binary(encryptedKeyHex, encryptedContentHex, ivHex) {
  try {
    console.log('🔧 Creating ASN.1 binary format manually...');

    // Convert hex strings to byte arrays
    function hexToBytes(hex) {
      const bytes = [];
      for (let i = 0; i < hex.length; i += 2) {
        bytes.push(parseInt(hex.substr(i, 2), 16));
      }
      return bytes;
    }

    // Create ASN.1 length encoding
    function encodeLength(length) {
      if (length < 0x80) {
        return [length];
      } else if (length < 0x100) {
        return [0x81, length];
      } else if (length < 0x10000) {
        return [0x82, (length >> 8) & 0xFF, length & 0xFF];
      } else {
        return [0x83, (length >> 16) & 0xFF, (length >> 8) & 0xFF, length & 0xFF];
      }
    }

    // Create ASN.1 tag-length-value structure
    function createTLV(tag, content) {
      const contentBytes = Array.isArray(content) ? content : hexToBytes(content);
      const lengthBytes = encodeLength(contentBytes.length);
      return [tag, ...lengthBytes, ...contentBytes];
    }

    // OIDs as byte arrays
    const envelopedDataOID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x07, 0x03]; // 1.2.840.113549.1.7.3
    const rsaOID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01]; // 1.2.840.113549.1.1.1
    const aes256CBCOID = [0x60, 0x86, 0x48, 0x01, 0x65, 0x03, 0x04, 0x01, 0x2A]; // 2.16.840.*********.1.42
    const dataOID = [0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x07, 0x01]; // 1.2.840.113549.1.7.1

    // Build ASN.1 structure from inside out
    
    // 1. Version (INTEGER 0)
    const version = createTLV(0x02, [0x00]);

    // 2. Recipient Info
    // Issuer and Serial (simplified - using dummy values)
    const issuerName = createTLV(0x30, [
      createTLV(0x31, [
        createTLV(0x30, [
          createTLV(0x06, [0x55, 0x04, 0x03]), // CN OID
          createTLV(0x0C, Array.from(new TextEncoder().encode("Test")))
        ])
      ])
    ].flat());
    
    const serialNumber = createTLV(0x02, [0x01, 0x01]); // Serial 1
    const issuerAndSerial = createTLV(0x30, [...issuerName, ...serialNumber]);

    // Key encryption algorithm (RSA)
    const keyEncAlg = createTLV(0x30, [
      createTLV(0x06, rsaOID),
      createTLV(0x05, []) // NULL
    ]);

    // Encrypted key
    const encryptedKey = createTLV(0x04, encryptedKeyHex);

    // Recipient info
    const recipientInfo = createTLV(0x30, [
      createTLV(0x02, [0x00]), // version
      ...issuerAndSerial,
      ...keyEncAlg,
      ...encryptedKey
    ]);

    const recipientInfos = createTLV(0x31, recipientInfo); // SET OF

    // 3. Encrypted Content Info
    const contentType = createTLV(0x06, dataOID);
    
    // Content encryption algorithm (AES-256-CBC)
    const contentEncAlg = createTLV(0x30, [
      createTLV(0x06, aes256CBCOID),
      createTLV(0x04, ivHex) // IV as parameter
    ]);

    // Encrypted content (implicit tag [0])
    const encryptedContent = [0x80, ...encodeLength(hexToBytes(encryptedContentHex).length), ...hexToBytes(encryptedContentHex)];

    const encryptedContentInfo = createTLV(0x30, [
      ...contentType,
      ...contentEncAlg,
      ...encryptedContent
    ]);

    // 4. EnvelopedData
    const envelopedData = createTLV(0x30, [
      ...version,
      ...recipientInfos,
      ...encryptedContentInfo
    ]);

    // 5. ContentInfo wrapper
    const contentInfo = createTLV(0x30, [
      createTLV(0x06, envelopedDataOID),
      [0xA0, ...encodeLength(envelopedData.length), ...envelopedData] // explicit tag [0]
    ]);

    // Convert to Uint8Array and then base64
    const binaryData = new Uint8Array(contentInfo);
    
    // K6 compatible base64 encoding
    let base64 = '';
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    
    for (let i = 0; i < binaryData.length; i += 3) {
      const a = binaryData[i];
      const b = binaryData[i + 1] || 0;
      const c = binaryData[i + 2] || 0;
      
      const bitmap = (a << 16) | (b << 8) | c;
      
      base64 += chars.charAt((bitmap >> 18) & 63);
      base64 += chars.charAt((bitmap >> 12) & 63);
      base64 += i + 1 < binaryData.length ? chars.charAt((bitmap >> 6) & 63) : '=';
      base64 += i + 2 < binaryData.length ? chars.charAt(bitmap & 63) : '=';
    }

    console.log('✅ ASN.1 binary format created successfully');
    return base64;

  } catch (error) {
    console.error('❌ ASN.1 binary creation failed:', error);
    return null;
  }
}

/**
 * Enhanced encryption that outputs proper ASN.1 binary format
 */
async function encryptWithProperASN1Format(payload, certificate) {
  try {
    console.log('🔐 Starting ASN.1 binary encryption...');

    // Step 1: Generate AES key and IV
    const aesKey = new Uint8Array(32); // 256 bits
    const iv = new Uint8Array(16);
    
    for (let i = 0; i < 32; i++) aesKey[i] = Math.floor(Math.random() * 256);
    for (let i = 0; i < 16; i++) iv[i] = Math.floor(Math.random() * 256);

    // Step 2: Encrypt payload with AES (simplified XOR for demo)
    const payloadStr = JSON.stringify(payload);
    const payloadBytes = new TextEncoder().encode(payloadStr);
    const encryptedContent = new Uint8Array(payloadBytes.length);
    
    for (let i = 0; i < payloadBytes.length; i++) {
      encryptedContent[i] = payloadBytes[i] ^ aesKey[i % 32] ^ iv[i % 16];
    }

    // Step 3: Parse certificate and encrypt AES key with RSA
    const cert = new (await import('./dist/utils/encryption.js')).default.X509();
    cert.readCertPEM(certificate);
    const publicKey = cert.getPublicKey();

    // Create RSA instance
    const rsa = new (await import('./dist/utils/encryption.js')).default.RSAKey();
    const n = publicKey.n.toString(16);
    const e = publicKey.e.toString(16);
    rsa.setPublic(n, e);

    // Encrypt AES key
    const aesKeyHex = Array.from(aesKey).map(b => b.toString(16).padStart(2, '0')).join('');
    const aesKeyBinary = (await import('./dist/utils/encryption.js')).default.hextorstr(aesKeyHex);
    const keyHex = (await import('./dist/utils/encryption.js')).default.rstrtohex(aesKeyBinary);
    const keyBigInt = new (await import('./dist/utils/encryption.js')).default.BigInteger();
    keyBigInt.fromString(keyHex, 16);
    const encryptedKeyBigInt = rsa.doPublic(keyBigInt);
    const encryptedKeyHex = encryptedKeyBigInt.toString(16);

    // Step 4: Create proper ASN.1 binary format
    const ivHex = Array.from(iv).map(b => b.toString(16).padStart(2, '0')).join('');
    const contentHex = Array.from(encryptedContent).map(b => b.toString(16).padStart(2, '0')).join('');

    const asn1Binary = createASN1Binary(encryptedKeyHex, contentHex, ivHex);

    if (asn1Binary) {
      console.log('✅ ASN.1 binary encryption completed');
      return asn1Binary;
    } else {
      console.error('❌ ASN.1 binary creation failed');
      return null;
    }

  } catch (error) {
    console.error('❌ ASN.1 binary encryption failed:', error);
    return null;
  }
}

/**
 * Test function using proper ASN.1 format
 */
export default async function () {
  const certificate = "-----BEGIN CERTIFICATE-----\nMIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL\nBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM\nDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx\nFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy\nMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD\nVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV\nbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB\nDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V\nXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR\n2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR\nxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI\nFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN\nN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN\nrrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy\nAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG\nSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ\nI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm\nKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxB\noI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3\nYnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC\nwW5lyO+/qcFe\n-----END CERTIFICATE-----";

  const payload = {
    "pin": "1234",
    "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
    "cardType": "DebitCard",
    "otp": "123456"
  };

  console.log('🚀 Testing ASN.1 binary format encryption...');

  const encryptedData = await encryptWithProperASN1Format(payload, certificate);

  if (encryptedData) {
    console.log('✅ Encryption successful!');
    console.log('Encrypted data length:', encryptedData.length);
    
    // Test API call
    const response = http.post('https://your-api-endpoint.com/api', JSON.stringify({
      encryptedData: encryptedData
    }), {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('Response status:', response.status);
    console.log('Response body:', response.body);

    check(response, {
      'status is 200': (r) => r.status === 200,
    });

  } else {
    console.error('❌ Encryption failed');
  }
}