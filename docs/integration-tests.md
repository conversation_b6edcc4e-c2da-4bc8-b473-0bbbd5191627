# K6 Encryption Utility Integration Tests

This document describes the comprehensive integration tests and K6 compatibility validation for the encryption utility.

## Overview

The integration tests validate that the K6 encryption utility works correctly in various scenarios:

1. **End-to-End Workflow Tests** - Complete encryption workflow validation
2. **K6 Compatibility Tests** - Environment and API compatibility validation  
3. **Performance Tests** - Concurrent encryption operations and performance metrics
4. **Unit Tests** - Comprehensive function-level testing
5. **Output Format Compatibility** - PKCS#7-like format validation

## Test Files

### Core Test Files

- `src/integration-test-encryption.ts` - Main integration test suite
- `src/k6-compatibility-test.ts` - K6 environment compatibility validation
- `src/performance-test-encryption.ts` - Performance and concurrent operation tests
- `src/utils/encryption.test.ts` - Comprehensive unit tests
- `src/run-integration-tests.ts` - Test orchestration and runner

### Supporting Files

- `test-integration.sh` - Shell script to run all tests
- `src/test-encryption-functionality.ts` - Existing functionality tests
- `src/test-validation-simple.ts` - Simple validation tests

## Test Scenarios

### 1. End-to-End Workflow Tests

**File**: `src/integration-test-encryption.ts`

**Scenarios**:
- Certificate parsing validation
- Input validation testing
- Complete encryption workflow for different payload sizes
- Error handling validation
- Output format compatibility

**K6 Configuration**:
```javascript
scenarios: {
  end_to_end: {
    executor: 'shared-iterations',
    vus: 1,
    iterations: 1,
    maxDuration: '30s'
  },
  concurrent_encryption: {
    executor: 'constant-vus',
    vus: 10,
    duration: '30s'
  },
  load_test: {
    executor: 'ramping-vus',
    startVUs: 1,
    stages: [
      { duration: '10s', target: 5 },
      { duration: '20s', target: 10 },
      { duration: '10s', target: 0 }
    ]
  }
}
```

### 2. K6 Compatibility Tests

**File**: `src/k6-compatibility-test.ts`

**Tests**:
- K6 global variables (`__VU`, `__ITER`, `__ENV`)
- JavaScript built-ins (JSON, Date, etc.)
- TypedArrays and binary data support
- Error handling mechanisms
- Encryption utility imports and basic functionality
- Performance characteristics

### 3. Performance Tests

**File**: `src/performance-test-encryption.ts`

**Scenarios**:
- **Stress Test**: 50 VUs for 60 seconds
- **Spike Test**: Ramping from 1 to 100 VUs quickly
- **Endurance Test**: 10 VUs for 5 minutes

**Metrics Tracked**:
- Encryption duration (p95 < 500ms, p99 < 1000ms)
- Success rate (> 99%)
- Throughput (> 10 operations/second)
- Memory usage estimation
- Concurrent operations

### 4. Unit Tests

**File**: `src/utils/encryption.test.ts`

**Test Categories**:
- Certificate parsing tests
- Input validation tests
- Encryption function tests
- Enhanced encryption with error handling
- Utility function tests
- Comprehensive validation tests

**Features**:
- Custom test framework compatible with K6
- Structured assertions with detailed error messages
- Async/await support for encryption operations

## Running the Tests

### Prerequisites

1. **Node.js and npm** - For building the project
2. **K6** - For running the tests
   ```bash
   # Install K6 (macOS)
   brew install k6
   
   # Or visit: https://k6.io/docs/getting-started/installation/
   ```

### Build the Project

```bash
npm run build
```

### Run All Integration Tests

```bash
./test-integration.sh
```

### Run Individual Test Suites

```bash
# K6 Compatibility Test
k6 run dist/k6-compatibility-test.js

# End-to-End Integration Test
k6 run -e K6_SCENARIO=end_to_end dist/integration-test-encryption.js

# Performance Test (short version)
k6 run -e K6_SCENARIO=performance --duration 30s --vus 5 dist/performance-test-encryption.js

# Unit Tests
k6 run dist/test-encryption-functionality.js

# Comprehensive Test Runner
k6 run -e K6_SCENARIO=full_integration dist/run-integration-tests.js
```

### Test Scenarios

You can run specific scenarios by setting the `K6_SCENARIO` environment variable:

```bash
# End-to-end workflow
k6 run -e K6_SCENARIO=end_to_end dist/integration-test-encryption.js

# Performance validation
k6 run -e K6_SCENARIO=performance dist/integration-test-encryption.js

# Load testing
k6 run -e K6_SCENARIO=load dist/integration-test-encryption.js

# Stress testing
k6 run -e K6_SCENARIO=stress_test dist/performance-test-encryption.js

# Spike testing
k6 run -e K6_SCENARIO=spike_test dist/performance-test-encryption.js

# Endurance testing
k6 run -e K6_SCENARIO=endurance_test dist/performance-test-encryption.js
```

## Test Coverage

### Functional Coverage

- ✅ Certificate parsing (valid/invalid formats)
- ✅ Input validation (payloads and certificates)
- ✅ RSA-OAEP encryption operations
- ✅ Error handling and structured error responses
- ✅ Base64 encoding/decoding
- ✅ Payload size validation
- ✅ Circular reference handling
- ✅ Security validation (XSS prevention)

### K6 Compatibility Coverage

- ✅ K6 environment variables and globals
- ✅ JavaScript built-in functions
- ✅ TypedArrays (Uint8Array, TextEncoder)
- ✅ Base64 operations (atob, btoa)
- ✅ JSON serialization/deserialization
- ✅ Error handling mechanisms
- ✅ Async/await support
- ✅ Import/export module system

### Performance Coverage

- ✅ Single encryption operations
- ✅ Concurrent encryption operations
- ✅ Memory usage patterns
- ✅ Throughput under load
- ✅ Error rates under stress
- ✅ Response time percentiles

### Output Format Coverage

- ✅ Base64 format validation
- ✅ RSA 2048-bit output length validation
- ✅ Binary data characteristics
- ✅ PKCS#7-like envelope compatibility
- ✅ Format consistency across operations

## Metrics and Thresholds

### Integration Test Thresholds

```javascript
thresholds: {
  'encryption_duration': ['p(95)<1000'],        // 95% under 1 second
  'encryption_success_rate': ['rate>0.95'],     // 95% success rate
  'checks': ['rate>0.95']                       // 95% of checks pass
}
```

### Performance Test Thresholds

```javascript
thresholds: {
  'encryption_duration': ['p(95)<500', 'p(99)<1000'],  // Response times
  'encryption_success_rate': ['rate>0.99'],             // 99% success rate
  'encryption_throughput': ['rate>10'],                 // 10+ ops/second
  'memory_usage': ['value<100'],                        // Memory indicator
  'checks': ['rate>0.95']                               // Check success rate
}
```

## Test Data

### Test Certificate

The tests use a self-signed RSA 2048-bit certificate valid until 2030:

```
Subject: C=AU, ST=Some-State, O=Internet Widgits Pty Ltd
Issuer: C=AU, ST=Some-State, O=Internet Widgits Pty Ltd
Key Size: 2048 bits
Algorithm: RSA
Valid Until: 2030-01-01
```

### Test Payloads

**Small Payload**:
```json
{
  "pin": "1234",
  "cardId": "****************"
}
```

**Medium Payload**:
```json
{
  "pin": "1234",
  "cardId": "****************",
  "cardType": "VISA",
  "otp": "123456",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "metadata": {
    "deviceId": "device-123",
    "sessionId": "session-456",
    "userAgent": "K6-LoadTest/1.0"
  }
}
```

**Large Payload**: Includes transaction history and additional metadata

## Expected Results

### Successful Test Run

```
=== K6 Encryption Utility Integration Tests ===
Scenario: end_to_end
VU: 1, Iteration: 0

=== End-to-End Encryption Workflow Test ===
Test 1: Certificate parsing validation
✅ Certificate parsing validation passed

Test 2: Input validation
✅ Input validation passed

Test 3: Complete encryption workflow
  Testing small payload...
    ✅ small payload encryption passed
  Testing medium payload...
    ✅ medium payload encryption passed
  Testing large payload...
    ✅ large payload encryption passed

=== Error Handling Test ===
Test 1: Invalid payload handling
✅ Invalid payload handling passed

Test 2: Invalid certificate handling
✅ Invalid certificate handling passed

Test 3: Payload too large handling
✅ Oversized payload handling passed

=== K6 Compatibility Test ===
Test 1: K6 environment detection
✅ K6 environment detection passed

Test 2: K6 data types compatibility
✅ K6 compatibility test passed

=== Output Format Compatibility Test ===
Test 1: Base64 format validation
✅ Base64 format validation passed

Test 2: Binary data characteristics
✅ Binary data characteristics correct

Test 3: PKCS#7-like envelope compatibility
✅ PKCS#7-like envelope compatibility test passed

✅ All integration tests passed!
```

### Performance Metrics

```
Final Performance Metrics:
- Average encryption duration: Tracked via metrics
- 95th percentile duration: Tracked via metrics
- Success rate: Tracked via metrics
- Total errors: Tracked via metrics
- Peak concurrent operations: Tracked via metrics
```

## Troubleshooting

### Common Issues

1. **K6 Not Installed**
   ```
   ❌ K6 is not installed. Please install K6 to run integration tests.
   Visit: https://k6.io/docs/getting-started/installation/
   ```

2. **Build Failures**
   ```bash
   npm run build
   # Check for TypeScript errors and fix them
   ```

3. **Certificate Expiration**
   - The test certificate is valid until 2030
   - If tests fail due to expiration, generate a new test certificate

4. **Performance Threshold Failures**
   - Adjust thresholds in test files if running on slower hardware
   - Reduce VU count or duration for resource-constrained environments

### Debug Mode

Run tests with verbose output:

```bash
k6 run --verbose dist/integration-test-encryption.js
```

### Custom Test Configuration

Modify test parameters by editing the respective test files:

- Adjust VU counts in `options.scenarios`
- Modify thresholds in `options.thresholds`
- Change test payloads in test data sections
- Update certificates if needed

## Continuous Integration

### GitHub Actions Example

```yaml
name: Integration Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run build
      - uses: grafana/k6-action@v0.2.0
        with:
          filename: dist/k6-compatibility-test.js
      - uses: grafana/k6-action@v0.2.0
        with:
          filename: dist/integration-test-encryption.js
```

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Add appropriate K6 metrics and thresholds
3. Include both positive and negative test cases
4. Update this documentation with new test descriptions
5. Ensure tests work in both K6 and development environments

## Requirements Validation

This test suite validates the following requirements from the specification:

- **Requirement 1.3**: PKCS#7 envelope format compatibility ✅
- **Requirement 4.2**: Performance characteristics under load ✅
- **Requirement 4.3**: Consistent performance with multiple operations ✅
- **Requirement 5.1**: K6 JavaScript environment compatibility ✅

All integration tests are designed to ensure the encryption utility meets the specified requirements and works reliably in K6 load testing scenarios.