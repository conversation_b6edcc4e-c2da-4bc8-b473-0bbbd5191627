# K6 Encryption Utility - Usage Examples

This document provides comprehensive examples of how to use the K6 encryption utility in your load testing scenarios.

## Table of Contents

1. [Basic Usage](#basic-usage)
2. [Advanced Usage with Error Handling](#advanced-usage-with-error-handling)
3. [Load Testing Scenarios](#load-testing-scenarios)
4. [Performance Testing](#performance-testing)
5. [Integration with K6 HTTP Requests](#integration-with-k6-http-requests)

## Basic Usage

### Simple Encryption Example

```typescript
import { payloadEncryptionFactory } from '../src/utils/encryption';
import { check } from 'k6';

// Sample X.509 certificate (replace with your actual certificate)
const CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==
-----END CERTIFICATE-----`;

export default async function () {
  // Define the payload to encrypt
  const payload = {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456"
  };

  // Encrypt the payload
  const encryptedData = await payloadEncryptionFactory(payload, CERTIFICATE);

  // Validate the result
  check(encryptedData, {
    'encryption successful': (result) => result !== null,
    'result is base64 string': (result) => result && typeof result === 'string' && result.length > 0,
    'result contains valid base64': (result) => {
      if (!result) return false;
      try {
        atob(result);
        return true;
      } catch {
        return false;
      }
    }
  });

  if (encryptedData) {
    console.log(`Encryption successful. Result length: ${encryptedData.length}`);
  } else {
    console.log('Encryption failed');
  }
}
```

### Using Environment Variables for Certificates

```typescript
import { payloadEncryptionFactory } from '../src/utils/encryption';
import { check } from 'k6';

export const options = {
  vus: 1,
  duration: '10s',
};

export default async function () {
  // Get certificate from environment variable
  const certificate = __ENV.ENCRYPTION_CERTIFICATE;
  
  if (!certificate) {
    throw new Error('ENCRYPTION_CERTIFICATE environment variable is required');
  }

  const payload = {
    userId: `user_${__VU}_${__ITER}`,
    timestamp: Date.now(),
    action: "login"
  };

  const encrypted = await payloadEncryptionFactory(payload, certificate);
  
  check(encrypted, {
    'payload encrypted successfully': (result) => result !== null
  });

  return encrypted;
}
```

### Ignoring Certificate Expiration for Testing

```typescript
import { 
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors 
} from '../src/utils/encryption';
import { check } from 'k6';

export default async function () {
  const payload = {
    testData: "sample data",
    timestamp: Date.now()
  };

  const certificate = __ENV.ENCRYPTION_CERTIFICATE;

  // Option 1: Basic function with expiration ignored
  const encrypted = await payloadEncryptionFactory(payload, certificate, true);
  
  check(encrypted, {
    'encryption successful with ignored expiration': (result) => result !== null
  });

  // Option 2: Enhanced function with expiration ignored
  const result = await payloadEncryptionFactoryWithErrors(payload, certificate, true);
  
  if (result.success) {
    console.log('Encryption successful despite expired certificate');
  } else {
    console.error('Encryption failed for other reasons:', result.error?.message);
  }

  check(result, {
    'enhanced encryption successful': (r) => r.success === true
  });
}
```

## Advanced Usage with Error Handling

### Using Enhanced Encryption with Detailed Errors

```typescript
import { 
  payloadEncryptionFactoryWithErrors,
  EncryptionErrorType,
  formatEncryptionError 
} from '../src/utils/encryption';
import { check } from 'k6';

export default async function () {
  const payload = {
    sensitiveData: "important information",
    userId: __VU,
    iteration: __ITER
  };

  const certificate = __ENV.ENCRYPTION_CERTIFICATE;

  // Use enhanced encryption function for detailed error reporting
  const result = await payloadEncryptionFactoryWithErrors(payload, certificate);

  if (result.success) {
    console.log(`Encryption successful: ${result.data?.substring(0, 50)}...`);
    
    check(result, {
      'encryption successful': (r) => r.success === true,
      'encrypted data exists': (r) => r.data !== undefined && r.data.length > 0
    });
    
    return result.data;
  } else {
    // Handle specific error types
    const error = result.error!;
    const formattedError = formatEncryptionError(error);
    
    console.error(`Encryption failed: ${formattedError}`);
    
    // Different handling based on error type
    switch (error.code) {
      case EncryptionErrorType.CERTIFICATE_EXPIRED:
        console.error('Certificate has expired - update certificate');
        break;
      case EncryptionErrorType.PAYLOAD_TOO_LARGE:
        console.error(`Payload too large: ${error.details?.payloadSize} bytes`);
        break;
      case EncryptionErrorType.INVALID_CERTIFICATE:
        console.error('Invalid certificate format');
        break;
      default:
        console.error('Unknown encryption error');
    }
    
    check(result, {
      'error handled properly': (r) => !r.success && r.error !== undefined
    });
    
    return null;
  }
}
```

### Input Validation Example

```typescript
import { 
  validateEncryptionInputs,
  validatePayloadInput,
  validateCertificateInput,
  comprehensiveValidation
} from '../src/utils/encryption';

export default function () {
  const payload = {
    data: "test data",
    timestamp: Date.now()
  };
  
  const certificate = __ENV.ENCRYPTION_CERTIFICATE;

  // Basic input validation
  const basicValidation = validateEncryptionInputs(payload, certificate);
  console.log('Basic validation:', basicValidation.success ? 'PASSED' : 'FAILED');

  // Individual validations
  const payloadValidation = validatePayloadInput(payload);
  const certValidation = validateCertificateInput(certificate);
  
  console.log('Payload validation:', payloadValidation.success ? 'PASSED' : 'FAILED');
  console.log('Certificate validation:', certValidation.success ? 'PASSED' : 'FAILED');

  // Comprehensive validation with safety checks
  const comprehensive = comprehensiveValidation(payload, certificate);
  console.log('Comprehensive validation:', comprehensive.isValid ? 'PASSED' : 'FAILED');
  
  if (!comprehensive.isValid) {
    console.log('Validation errors:', comprehensive.errors.length);
    console.log('Safety issues:', comprehensive.safetyIssues.length);
    console.log('Warnings:', comprehensive.warnings.length);
  }
}
```

## Load Testing Scenarios

### Concurrent Encryption Load Test

```typescript
import { payloadEncryptionFactory } from '../src/utils/encryption';
import { check, sleep } from 'k6';
import { Rate } from 'k6/metrics';

// Custom metrics
const encryptionSuccessRate = new Rate('encryption_success_rate');
const encryptionErrorRate = new Rate('encryption_error_rate');

export const options = {
  stages: [
    { duration: '2m', target: 10 },   // Ramp up to 10 users
    { duration: '5m', target: 10 },   // Stay at 10 users
    { duration: '2m', target: 50 },   // Ramp up to 50 users
    { duration: '5m', target: 50 },   // Stay at 50 users
    { duration: '2m', target: 0 },    // Ramp down to 0 users
  ],
  thresholds: {
    encryption_success_rate: ['rate>0.95'], // 95% success rate
    encryption_error_rate: ['rate<0.05'],   // Less than 5% errors
  },
};

const CERTIFICATE = __ENV.ENCRYPTION_CERTIFICATE;

export default async function () {
  const startTime = Date.now();
  
  // Generate unique payload for each iteration
  const payload = {
    userId: `user_${__VU}`,
    sessionId: `session_${__VU}_${__ITER}`,
    timestamp: Date.now(),
    data: {
      action: "process_payment",
      amount: Math.floor(Math.random() * 1000) + 1,
      currency: "USD",
      cardNumber: "****************"
    }
  };

  try {
    const encrypted = await payloadEncryptionFactory(payload, CERTIFICATE);
    const encryptionTime = Date.now() - startTime;
    
    const success = encrypted !== null;
    encryptionSuccessRate.add(success);
    encryptionErrorRate.add(!success);
    
    check(encrypted, {
      'encryption completed': (result) => result !== null,
      'encryption time acceptable': () => encryptionTime < 1000, // Less than 1 second
      'result is valid base64': (result) => {
        if (!result) return false;
        try {
          atob(result);
          return true;
        } catch {
          return false;
        }
      }
    });

    if (success) {
      console.log(`VU ${__VU}, Iter ${__ITER}: Encryption successful in ${encryptionTime}ms`);
    } else {
      console.error(`VU ${__VU}, Iter ${__ITER}: Encryption failed`);
    }

  } catch (error) {
    encryptionErrorRate.add(true);
    encryptionSuccessRate.add(false);
    console.error(`VU ${__VU}, Iter ${__ITER}: Exception during encryption: ${error}`);
  }

  sleep(1); // 1 second pause between iterations
}
```

### Stress Testing with Large Payloads

```typescript
import { payloadEncryptionFactory } from '../src/utils/encryption';
import { check } from 'k6';

export const options = {
  vus: 20,
  duration: '30s',
};

const CERTIFICATE = __ENV.ENCRYPTION_CERTIFICATE;

export default async function () {
  // Test with various payload sizes
  const payloadSizes = [
    { name: 'small', size: 50 },
    { name: 'medium', size: 100 },
    { name: 'large', size: 150 },
    { name: 'max_safe', size: 190 } // Close to RSA-2048 limit (~214 bytes)
  ];

  const testCase = payloadSizes[__ITER % payloadSizes.length];
  
  // Generate payload of specified size
  const basePayload = {
    type: testCase.name,
    userId: __VU,
    iteration: __ITER,
    timestamp: Date.now()
  };
  
  // Add padding to reach desired size
  const currentSize = JSON.stringify(basePayload).length;
  const paddingNeeded = Math.max(0, testCase.size - currentSize);
  
  const payload = {
    ...basePayload,
    padding: 'x'.repeat(paddingNeeded)
  };

  const actualSize = JSON.stringify(payload).length;
  console.log(`Testing ${testCase.name} payload: ${actualSize} bytes`);

  const encrypted = await payloadEncryptionFactory(payload, CERTIFICATE);
  
  check(encrypted, {
    [`${testCase.name}_payload_encrypted`]: (result) => result !== null,
    [`${testCase.name}_size_within_limits`]: () => actualSize <= testCase.size + 10 // Allow small variance
  });
}
```

## Performance Testing

### Encryption Performance Benchmark

```typescript
import { payloadEncryptionFactory } from '../src/utils/encryption';
import { Trend } from 'k6/metrics';

// Custom metrics for performance tracking
const encryptionDuration = new Trend('encryption_duration');
const payloadSizeMetric = new Trend('payload_size_bytes');

export const options = {
  vus: 1,
  iterations: 100,
  thresholds: {
    encryption_duration: ['avg<500', 'p(95)<1000'], // Average < 500ms, 95th percentile < 1s
  },
};

const CERTIFICATE = __ENV.ENCRYPTION_CERTIFICATE;

export default async function () {
  const payload = {
    benchmarkId: __ITER,
    timestamp: Date.now(),
    data: {
      field1: "value1",
      field2: "value2",
      field3: Math.random().toString(36),
      field4: Array.from({length: 10}, () => Math.random())
    }
  };

  const payloadSize = JSON.stringify(payload).length;
  payloadSizeMetric.add(payloadSize);

  const startTime = Date.now();
  const encrypted = await payloadEncryptionFactory(payload, CERTIFICATE);
  const duration = Date.now() - startTime;
  
  encryptionDuration.add(duration);

  if (encrypted) {
    console.log(`Iteration ${__ITER}: ${payloadSize} bytes encrypted in ${duration}ms`);
  } else {
    console.error(`Iteration ${__ITER}: Encryption failed`);
  }
}
```

## Integration with K6 HTTP Requests

### Encrypted API Request Example

```typescript
import http from 'k6/http';
import { check } from 'k6';
import { payloadEncryptionFactory } from '../src/utils/encryption';

export const options = {
  vus: 10,
  duration: '1m',
};

const CERTIFICATE = __ENV.ENCRYPTION_CERTIFICATE;
const API_BASE_URL = __ENV.API_BASE_URL || 'https://api.example.com';

export default async function () {
  // Prepare sensitive payload
  const sensitivePayload = {
    userId: `user_${__VU}`,
    creditCard: {
      number: "****************",
      cvv: "123",
      expiryMonth: "12",
      expiryYear: "2025"
    },
    amount: 99.99,
    currency: "USD"
  };

  // Encrypt the sensitive payload
  const encryptedPayload = await payloadEncryptionFactory(sensitivePayload, CERTIFICATE);
  
  if (!encryptedPayload) {
    console.error('Failed to encrypt payload');
    return;
  }

  // Prepare the API request with encrypted data
  const requestPayload = {
    encryptedData: encryptedPayload,
    requestId: `req_${__VU}_${__ITER}`,
    timestamp: Date.now()
  };

  const params = {
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': __ENV.API_KEY || 'test-key'
    },
  };

  // Make the API request
  const response = http.post(
    `${API_BASE_URL}/secure-payment`,
    JSON.stringify(requestPayload),
    params
  );

  // Validate the response
  check(response, {
    'status is 200': (r) => r.status === 200,
    'response time < 2000ms': (r) => r.timings.duration < 2000,
    'response contains success': (r) => r.body && r.body.includes('success'),
  });

  console.log(`VU ${__VU}, Iter ${__ITER}: API call completed with status ${response.status}`);
}
```

### Batch Encryption for Multiple Requests

```typescript
import http from 'k6/http';
import { check, group } from 'k6';
import { payloadEncryptionFactory } from '../src/utils/encryption';

export const options = {
  vus: 5,
  duration: '30s',
};

const CERTIFICATE = __ENV.ENCRYPTION_CERTIFICATE;
const API_BASE_URL = __ENV.API_BASE_URL || 'https://api.example.com';

export default async function () {
  // Prepare multiple payloads for batch processing
  const payloads = [
    { type: 'login', username: `user${__VU}`, password: 'secret123' },
    { type: 'profile_update', userId: __VU, email: `user${__VU}@example.com` },
    { type: 'payment', amount: 50.00, cardId: 'card123' }
  ];

  group('Batch Encryption and API Calls', () => {
    const encryptedPayloads = [];

    // Encrypt all payloads
    group('Encryption Phase', async () => {
      for (let i = 0; i < payloads.length; i++) {
        const encrypted = await payloadEncryptionFactory(payloads[i], CERTIFICATE);
        
        check(encrypted, {
          [`payload_${i}_encrypted`]: (result) => result !== null
        });

        if (encrypted) {
          encryptedPayloads.push({
            index: i,
            type: payloads[i].type,
            encryptedData: encrypted
          });
        }
      }
    });

    // Send all encrypted payloads
    group('API Requests Phase', () => {
      encryptedPayloads.forEach((item, index) => {
        const response = http.post(
          `${API_BASE_URL}/process-encrypted`,
          JSON.stringify({
            batchId: `batch_${__VU}_${__ITER}`,
            itemIndex: item.index,
            type: item.type,
            encryptedData: item.encryptedData
          }),
          {
            headers: { 'Content-Type': 'application/json' }
          }
        );

        check(response, {
          [`batch_item_${index}_success`]: (r) => r.status === 200
        });
      });
    });
  });
}
```

## Error Handling and Debugging

### Comprehensive Error Handling Example

```typescript
import { 
  payloadEncryptionFactoryWithErrors,
  EncryptionErrorType,
  formatEncryptionError,
  parsePEMCertificate,
  validateCertificateFormat
} from '../src/utils/encryption';

export default async function () {
  const certificate = __ENV.ENCRYPTION_CERTIFICATE;
  
  // Pre-validate certificate
  console.log('=== Certificate Validation ===');
  const certFormat = validateCertificateFormat(certificate);
  if (!certFormat.isValid) {
    console.error('Certificate format validation failed:');
    certFormat.errors.forEach(error => console.error(`  - ${error}`));
    return;
  }

  const certInfo = parsePEMCertificate(certificate);
  if (!certInfo.valid) {
    console.error('Certificate parsing failed');
    if (certInfo.isExpired) {
      console.error(`Certificate expired on: ${certInfo.expirationDate}`);
    }
    return;
  }

  console.log(`Certificate valid: ${certInfo.keySize}-bit ${certInfo.algorithm} key`);
  console.log(`Expires: ${certInfo.expirationDate}`);

  // Test various payload scenarios
  const testCases = [
    { name: 'Valid payload', payload: { test: 'data' } },
    { name: 'Null payload', payload: null },
    { name: 'Empty object', payload: {} },
    { name: 'Large payload', payload: { data: 'x'.repeat(300) } },
    { name: 'Complex payload', payload: { 
      user: 'test', 
      nested: { deep: { value: 'test' } },
      array: [1, 2, 3, 4, 5]
    }}
  ];

  for (const testCase of testCases) {
    console.log(`\n=== Testing: ${testCase.name} ===`);
    
    const result = await payloadEncryptionFactoryWithErrors(testCase.payload, certificate);
    
    if (result.success) {
      console.log(`✓ Success: ${result.data?.length} characters`);
    } else {
      console.log(`✗ Failed: ${formatEncryptionError(result.error!)}`);
      
      // Provide specific guidance based on error type
      switch (result.error?.code) {
        case EncryptionErrorType.PAYLOAD_TOO_LARGE:
          console.log('  → Try reducing payload size or use a larger RSA key');
          break;
        case EncryptionErrorType.INVALID_PAYLOAD:
          console.log('  → Ensure payload is not null/empty and is JSON serializable');
          break;
        case EncryptionErrorType.CERTIFICATE_EXPIRED:
          console.log('  → Update to a valid, non-expired certificate');
          break;
      }
    }
  }
}
```

This completes the usage examples section. The examples cover basic usage, advanced error handling, load testing scenarios, performance testing, and integration with HTTP requests.