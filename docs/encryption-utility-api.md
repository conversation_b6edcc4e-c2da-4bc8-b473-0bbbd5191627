# K6 Encryption Utility - API Reference

This document provides detailed API documentation for the K6 encryption utility functions and interfaces.

## Table of Contents

1. [Main Functions](#main-functions)
2. [Validation Functions](#validation-functions)
3. [Utility Functions](#utility-functions)
4. [Interfaces and Types](#interfaces-and-types)
5. [Error Handling](#error-handling)
6. [Constants and Enums](#constants-and-enums)

## Main Functions

### `payloadEncryptionFactory(payload, certificate, ignoreExpiration?)`

The primary encryption function that encrypts JSON payloads using X.509 certificates.

**Signature:**
```typescript
async function payloadEncryptionFactory(
  payload: any,
  certificate: string,
  ignoreExpiration?: boolean
): Promise<string | null>
```

**Parameters:**
- `payload` (any): The JSON-serializable payload to encrypt
- `certificate` (string): PEM-formatted X.509 certificate containing the public key
- `ignoreExpiration` (boolean, optional): If `true`, skip certificate expiration validation (useful for testing). Default: `false`

**Returns:**
- `Promise<string | null>`: Base64-encoded encrypted data on success, `null` on failure

**Example:**
```typescript
const encrypted = await payloadEncryptionFactory(
  { pin: "1234", cardId: "****************" },
  pemCertificate
);
```

**Behavior:**
- Validates inputs before processing
- Serializes payload to JSON string
- Extracts public key from certificate
- Performs RSA-OAEP encryption
- Returns base64-encoded result
- Returns `null` on any failure (use `payloadEncryptionFactoryWithErrors` for detailed errors)

---

### `payloadEncryptionFactoryWithErrors(payload, certificate, ignoreExpiration?)`

Enhanced encryption function that provides detailed error information.

**Signature:**
```typescript
async function payloadEncryptionFactoryWithErrors(
  payload: any,
  certificate: string,
  ignoreExpiration?: boolean
): Promise<EnhancedEncryptionResult>
```

**Parameters:**
- `payload` (any): The JSON-serializable payload to encrypt
- `certificate` (string): PEM-formatted X.509 certificate
- `ignoreExpiration` (boolean, optional): If `true`, skip certificate expiration validation (useful for testing). Default: `false`

**Returns:**
- `Promise<EnhancedEncryptionResult>`: Detailed result with success status and error information

**Example:**
```typescript
const result = await payloadEncryptionFactoryWithErrors(payload, certificate);
if (result.success) {
  console.log('Encrypted:', result.data);
} else {
  console.error('Error:', formatEncryptionError(result.error));
}
```

---

### `parsePEMCertificate(pemCertificate, ignoreExpiration?)`

Parses a PEM-formatted X.509 certificate and extracts public key information.

**Signature:**
```typescript
function parsePEMCertificate(
  pemCertificate: string, 
  ignoreExpiration?: boolean
): CertificateInfo
```

**Parameters:**
- `pemCertificate` (string): PEM-formatted X.509 certificate
- `ignoreExpiration` (boolean, optional): If `true`, skip expiration validation (useful for testing). Default: `false`

**Returns:**
- `CertificateInfo`: Object containing parsed certificate information

**Example:**
```typescript
const certInfo = parsePEMCertificate(pemCertificate);
if (certInfo.valid) {
  console.log(`Key size: ${certInfo.keySize} bits`);
  console.log(`Algorithm: ${certInfo.algorithm}`);
  console.log(`Expires: ${certInfo.expirationDate}`);
}
```

---

### `encryptWithRSAOAEP(message, publicKey)`

Low-level RSA encryption function using OAEP padding.

**Signature:**
```typescript
function encryptWithRSAOAEP(message: string, publicKey: any): string | null
```

**Parameters:**
- `message` (string): The message to encrypt
- `publicKey` (any): RSA public key object from jsrsasign

**Returns:**
- `string | null`: Base64-encoded encrypted data on success, `null` on failure

**Example:**
```typescript
const certInfo = parsePEMCertificate(certificate);
if (certInfo.valid) {
  const encrypted = encryptWithRSAOAEP("Hello World", certInfo.publicKey);
}
```

## Validation Functions

### `validateEncryptionInputs(payload, certificate)`

Comprehensive validation of encryption inputs.

**Signature:**
```typescript
function validateEncryptionInputs(
  payload: any,
  certificate: string
): EnhancedEncryptionResult
```

**Parameters:**
- `payload` (any): Payload to validate
- `certificate` (string): Certificate to validate

**Returns:**
- `EnhancedEncryptionResult`: Validation result with detailed error information

**Example:**
```typescript
const validation = validateEncryptionInputs(payload, certificate);
if (!validation.success) {
  console.error('Validation failed:', validation.error?.message);
}
```

---

### `validatePayloadInput(payload)`

Validates payload input with comprehensive checks.

**Signature:**
```typescript
function validatePayloadInput(payload: any): EnhancedEncryptionResult
```

**Parameters:**
- `payload` (any): Payload to validate

**Returns:**
- `EnhancedEncryptionResult`: Validation result with serialized payload data

**Validation Checks:**
- Not null or undefined
- Not empty object or array
- Not empty string
- JSON serializable
- Size within reasonable limits (10KB default)

---

### `validateCertificateInput(certificate)`

Validates certificate input format and structure.

**Signature:**
```typescript
function validateCertificateInput(certificate: string): EnhancedEncryptionResult
```

**Parameters:**
- `certificate` (string): Certificate string to validate

**Returns:**
- `EnhancedEncryptionResult`: Validation result

**Validation Checks:**
- Not empty or null
- Is string type
- Valid PEM format
- Contains certificate markers
- Proper base64 encoding

---

### `validatePayloadSize(payloadSize, keySize)`

Validates payload size against RSA key constraints.

**Signature:**
```typescript
function validatePayloadSize(payloadSize: number, keySize: number): boolean
```

**Parameters:**
- `payloadSize` (number): Size of payload in bytes
- `keySize` (number): RSA key size in bits

**Returns:**
- `boolean`: True if payload size is valid for the key size

**Formula:**
For RSA-OAEP with SHA-1: `maxSize = (keySize/8) - 2*20 - 2`

**Example:**
```typescript
const isValid = validatePayloadSize(150, 2048); // true for 2048-bit key
const isValid2 = validatePayloadSize(300, 2048); // false - too large
```

---

### `comprehensiveValidation(payload, certificate)`

Performs comprehensive validation including safety checks.

**Signature:**
```typescript
function comprehensiveValidation(
  payload: any,
  certificate: string
): {
  isValid: boolean;
  errors: EncryptionError[];
  warnings: string[];
  safetyIssues: string[];
}
```

**Parameters:**
- `payload` (any): Payload to validate
- `certificate` (string): Certificate to validate

**Returns:**
- Object with detailed validation results including safety issues and warnings

## Utility Functions

### `validateCertificateFormat(pemCertificate)`

Validates PEM certificate format structure.

**Signature:**
```typescript
function validateCertificateFormat(pemCertificate: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
```

**Parameters:**
- `pemCertificate` (string): PEM certificate to validate

**Returns:**
- Object with validation results and detailed error/warning messages

---

### `formatEncryptionError(error)`

Formats encryption errors for logging and display.

**Signature:**
```typescript
function formatEncryptionError(error: EncryptionError): string
```

**Parameters:**
- `error` (EncryptionError): Error object to format

**Returns:**
- `string`: Formatted error message with details

**Example:**
```typescript
const formatted = formatEncryptionError(result.error);
console.error(formatted);
// Output: [INVALID_PAYLOAD] Payload is required and cannot be null
//         Details: { "actualType": "object" }
```

---

### `handleCircularReferences(obj)`

Handles circular references in objects before JSON serialization.

**Signature:**
```typescript
function handleCircularReferences(obj: any): any
```

**Parameters:**
- `obj` (any): Object to process

**Returns:**
- `any`: Object with circular references replaced with placeholders

---

### `validatePayloadSafety(payload)`

Validates payload for potential security and performance issues.

**Signature:**
```typescript
function validatePayloadSafety(payload: any): {
  isSafe: boolean;
  issues: string[];
}
```

**Parameters:**
- `payload` (any): Payload to check

**Returns:**
- Object with safety status and list of issues found

**Safety Checks:**
- Extremely large payloads (>1MB)
- Deeply nested objects (>100 levels)
- Potential script injection content

## Interfaces and Types

### `CertificateInfo`

Information extracted from parsed X.509 certificates.

```typescript
interface CertificateInfo {
  publicKey: any;           // RSA public key object
  valid: boolean;           // Whether certificate is valid
  keySize?: number;         // Key size in bits
  algorithm?: string;       // Key algorithm (e.g., "RSA")
  expirationDate?: Date;    // Certificate expiration date
  issuer?: string;          // Certificate issuer
  subject?: string;         // Certificate subject
  serialNumber?: string;    // Certificate serial number
  isExpired?: boolean;      // Whether certificate has expired
}
```

---

### `EncryptionResult`

Basic encryption result interface.

```typescript
interface EncryptionResult {
  success: boolean;
  data?: string;
  error?: string;
}
```

---

### `EnhancedEncryptionResult`

Enhanced encryption result with structured error information.

```typescript
interface EnhancedEncryptionResult {
  success: boolean;
  data?: string;
  error?: EncryptionError;
}
```

---

### `EncryptionError`

Structured error information for encryption operations.

```typescript
interface EncryptionError {
  code: EncryptionErrorType;
  message: string;
  details?: any;
}
```

## Error Handling

### Error Types

The utility uses structured error codes for different failure scenarios:

```typescript
enum EncryptionErrorType {
  INVALID_PAYLOAD = 'INVALID_PAYLOAD',
  INVALID_CERTIFICATE = 'INVALID_CERTIFICATE',
  CERTIFICATE_EXPIRED = 'CERTIFICATE_EXPIRED',
  CERTIFICATE_NOT_YET_VALID = 'CERTIFICATE_NOT_YET_VALID',
  UNSUPPORTED_KEY_TYPE = 'UNSUPPORTED_KEY_TYPE',
  INSUFFICIENT_KEY_SIZE = 'INSUFFICIENT_KEY_SIZE',
  PAYLOAD_TOO_LARGE = 'PAYLOAD_TOO_LARGE',
  ENCRYPTION_FAILED = 'ENCRYPTION_FAILED',
  SERIALIZATION_FAILED = 'SERIALIZATION_FAILED',
  CERTIFICATE_PARSING_FAILED = 'CERTIFICATE_PARSING_FAILED'
}
```

### Error Handling Best Practices

1. **Use Enhanced Functions**: Prefer `payloadEncryptionFactoryWithErrors` for detailed error information
2. **Check Error Codes**: Use error codes to handle specific failure scenarios
3. **Log Formatted Errors**: Use `formatEncryptionError` for consistent error logging
4. **Validate Early**: Use validation functions before attempting encryption
5. **Handle Gracefully**: Always check for null returns from basic functions

### Common Error Scenarios

#### Certificate Expired
```typescript
const result = await payloadEncryptionFactoryWithErrors(payload, certificate);
if (!result.success && result.error?.code === EncryptionErrorType.CERTIFICATE_EXPIRED) {
  console.error('Certificate expired:', result.error.details?.expirationDate);
  // Handle certificate renewal
}
```

#### Payload Too Large
```typescript
if (result.error?.code === EncryptionErrorType.PAYLOAD_TOO_LARGE) {
  const { payloadSize, maxSize } = result.error.details;
  console.error(`Payload ${payloadSize} bytes exceeds limit of ${maxSize} bytes`);
  // Reduce payload size or use larger key
}
```

#### Invalid Certificate Format
```typescript
if (result.error?.code === EncryptionErrorType.INVALID_CERTIFICATE) {
  console.error('Invalid certificate format');
  // Check certificate PEM format
}
```

## Constants and Enums

### Key Size Requirements

- **Minimum Key Size**: 2048 bits (for security)
- **Supported Algorithms**: RSA only
- **Padding**: OAEP with SHA-1

### Payload Size Limits

- **RSA-2048**: ~214 bytes maximum payload
- **RSA-3072**: ~342 bytes maximum payload  
- **RSA-4096**: ~470 bytes maximum payload

### Certificate Format Requirements

- **Format**: PEM (Privacy-Enhanced Mail)
- **Type**: X.509 certificates
- **Encoding**: Base64 with proper headers/footers
- **Headers**: Must contain `-----BEGIN CERTIFICATE-----` and `-----END CERTIFICATE-----`

## Ignore Expiration Feature

For testing purposes, you can bypass certificate expiration validation using the `ignoreExpiration` parameter:

```typescript
// Skip expiration check for testing
const encrypted = await payloadEncryptionFactory(payload, certificate, true);

// With detailed error handling
const result = await payloadEncryptionFactoryWithErrors(payload, certificate, true);

// Certificate parsing without expiration check
const certInfo = parsePEMCertificate(certificate, true);
```

**Use Cases:**
- Testing with expired certificates
- Development environments with incorrect system dates
- Load testing scenarios where certificate renewal is not feasible

**Security Note:** Only use `ignoreExpiration = true` in testing environments. Production code should always validate certificate expiration for security.

## Usage Notes

1. **K6 Compatibility**: All functions are designed to work within K6's JavaScript runtime (v1.1.1)
2. **Dependencies**: Requires `jsrsasign` library for cryptographic operations
3. **Performance**: Encryption operations are CPU-intensive; consider this in load testing scenarios
4. **Security**: Uses RSA-OAEP padding which provides semantic security
5. **Error Handling**: Always check return values and handle errors appropriately
6. **Certificate Management**: Ensure certificates are valid and not expired before use (unless using `ignoreExpiration` for testing)