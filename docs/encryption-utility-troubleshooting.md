# K6 Encryption Utility - Troubleshooting Guide

This guide helps you diagnose and resolve common issues when using the K6 encryption utility.

## Table of Contents

1. [Common Issues](#common-issues)
2. [Error Messages and Solutions](#error-messages-and-solutions)
3. [Certificate Problems](#certificate-problems)
4. [Payload Issues](#payload-issues)
5. [Performance Problems](#performance-problems)
6. [K6 Environment Issues](#k6-environment-issues)
7. [Debugging Techniques](#debugging-techniques)
8. [FAQ](#faq)

## Common Issues

### 1. Encryption Returns Null

**Symptoms:**
- `payloadEncryptionFactory` returns `null`
- No error messages in basic function

**Diagnosis:**
```typescript
// Use enhanced function for detailed errors
const result = await payloadEncryptionFactoryWithErrors(payload, certificate);
if (!result.success) {
  console.error('Detailed error:', formatEncryptionError(result.error));
}
```

**Common Causes:**
- Invalid certificate format
- Expired certificate
- Payload too large for key size
- Invalid payload (null, empty, non-serializable)

---

### 2. Certificate Parsing Fails

**Symptoms:**
- Certificate validation fails
- `parsePEMCertificate` returns `valid: false`

**Diagnosis:**
```typescript
import { validateCertificateFormat, parsePEMCertificate } from './utils/encryption';

// Check certificate format first
const formatCheck = validateCertificateFormat(certificate);
if (!formatCheck.isValid) {
  console.error('Format errors:', formatCheck.errors);
  console.warn('Format warnings:', formatCheck.warnings);
}

// Then check parsing
const certInfo = parsePEMCertificate(certificate);
console.log('Certificate info:', certInfo);
```

**Solutions:**
- Verify PEM format with proper headers/footers
- Check for extra whitespace or line ending issues
- Ensure certificate is not corrupted
- Verify certificate is X.509 format

---

### 3. Payload Too Large Error

**Symptoms:**
- Error code: `PAYLOAD_TOO_LARGE`
- Encryption fails with size-related error

**Diagnosis:**
```typescript
import { validatePayloadSize } from './utils/encryption';

const payloadString = JSON.stringify(payload);
const payloadSize = new TextEncoder().encode(payloadString).length;
const keySize = 2048; // Your certificate key size

console.log(`Payload size: ${payloadSize} bytes`);
console.log(`Key size: ${keySize} bits`);
console.log(`Max payload size: ${Math.floor(keySize / 8) - 42} bytes`);

const isValid = validatePayloadSize(payloadSize, keySize);
console.log(`Size valid: ${isValid}`);
```

**Solutions:**
- Reduce payload size by removing unnecessary fields
- Use a larger RSA key (3072 or 4096 bits)
- Split large payloads into multiple smaller ones
- Compress data before encryption (if supported by your system)

---

### 4. K6 Import Errors

**Symptoms:**
- Module import failures in K6
- "Module not found" errors

**Diagnosis:**
```typescript
// Check if the module is properly bundled
try {
  import { payloadEncryptionFactory } from './utils/encryption';
  console.log('Import successful');
} catch (error) {
  console.error('Import failed:', error);
}
```

**Solutions:**
- Ensure rollup build includes the encryption utility
- Check file paths in imports
- Verify the built bundle contains the encryption module
- Use relative paths from your K6 script location

## Error Messages and Solutions

### `INVALID_PAYLOAD` Errors

#### "Payload is required and cannot be null or undefined"
```typescript
// ❌ Wrong
const payload = null;
const encrypted = await payloadEncryptionFactory(payload, certificate);

// ✅ Correct
const payload = { data: "value" };
const encrypted = await payloadEncryptionFactory(payload, certificate);
```

#### "Payload cannot be an empty object"
```typescript
// ❌ Wrong
const payload = {};

// ✅ Correct
const payload = { field: "value" };
```

#### "Payload must be JSON serializable"
```typescript
// ❌ Wrong - circular reference
const payload = { name: "test" };
payload.self = payload;

// ✅ Correct - use handleCircularReferences
import { handleCircularReferences } from './utils/encryption';
const cleanPayload = handleCircularReferences(payload);
```

### `INVALID_CERTIFICATE` Errors

#### "Certificate is required and cannot be empty"
```typescript
// ❌ Wrong
const certificate = "";

// ✅ Correct
const certificate = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
...
-----END CERTIFICATE-----`;
```

#### "Invalid certificate format"
```typescript
// ❌ Wrong - missing headers
const certificate = "MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV...";

// ✅ Correct - proper PEM format
const certificate = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
...
-----END CERTIFICATE-----`;
```

### `CERTIFICATE_EXPIRED` Errors

#### "Certificate has expired"
```typescript
// Check certificate expiration
const certInfo = parsePEMCertificate(certificate);
if (certInfo.isExpired) {
  console.error(`Certificate expired on: ${certInfo.expirationDate}`);
  // Solution: Get a new, valid certificate
  // OR for testing: use ignoreExpiration = true
}

// Temporary workaround for testing
const encrypted = await payloadEncryptionFactory(payload, certificate, true);
```

### `PAYLOAD_TOO_LARGE` Errors

#### Size calculation and limits
```typescript
// Calculate actual limits for your key
function calculateMaxPayloadSize(keyBits) {
  // RSA-OAEP with SHA-1: (keySize/8) - 2*hashLength - 2
  return Math.floor(keyBits / 8) - (2 * 20) - 2;
}

console.log('RSA-2048 max payload:', calculateMaxPayloadSize(2048), 'bytes'); // ~214 bytes
console.log('RSA-3072 max payload:', calculateMaxPayloadSize(3072), 'bytes'); // ~342 bytes
console.log('RSA-4096 max payload:', calculateMaxPayloadSize(4096), 'bytes'); // ~470 bytes
```

## Certificate Problems

### Invalid PEM Format

**Problem:** Certificate doesn't have proper PEM structure

**Check:**
```typescript
function checkCertificateFormat(cert) {
  const issues = [];
  
  if (!cert.includes('-----BEGIN CERTIFICATE-----')) {
    issues.push('Missing BEGIN CERTIFICATE header');
  }
  
  if (!cert.includes('-----END CERTIFICATE-----')) {
    issues.push('Missing END CERTIFICATE footer');
  }
  
  const lines = cert.split('\n');
  const certLines = lines.slice(1, -1); // Exclude header/footer
  
  for (const line of certLines) {
    if (line.trim() && !/^[A-Za-z0-9+/=]+$/.test(line.trim())) {
      issues.push(`Invalid base64 character in line: ${line}`);
    }
  }
  
  return issues;
}
```

**Solution:**
- Ensure proper PEM headers and footers
- Verify base64 encoding is correct
- Check for extra characters or formatting issues

### Certificate Expiration

**Problem:** Certificate has expired or is not yet valid

**Check:**
```typescript
function checkCertificateValidity(certificate) {
  const certInfo = parsePEMCertificate(certificate);
  const now = new Date();
  
  if (certInfo.expirationDate) {
    const daysUntilExpiry = Math.floor(
      (certInfo.expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysUntilExpiry < 0) {
      console.error(`Certificate expired ${Math.abs(daysUntilExpiry)} days ago`);
    } else if (daysUntilExpiry < 30) {
      console.warn(`Certificate expires in ${daysUntilExpiry} days`);
    } else {
      console.log(`Certificate valid for ${daysUntilExpiry} more days`);
    }
  }
}
```

**Solution:**
- Obtain a new, valid certificate
- Check certificate renewal processes
- Set up monitoring for certificate expiration
- **For testing only**: Use `ignoreExpiration = true` parameter

### Unsupported Key Types

**Problem:** Certificate uses non-RSA keys or insufficient key size

**Check:**
```typescript
function checkKeyRequirements(certificate) {
  const certInfo = parsePEMCertificate(certificate);
  
  if (certInfo.algorithm !== 'RSA') {
    console.error(`Unsupported algorithm: ${certInfo.algorithm}. Only RSA is supported.`);
    return false;
  }
  
  if (certInfo.keySize < 2048) {
    console.error(`Key size ${certInfo.keySize} is below minimum 2048 bits`);
    return false;
  }
  
  console.log(`✓ Valid ${certInfo.keySize}-bit RSA key`);
  return true;
}
```

**Solution:**
- Use RSA certificates only
- Ensure minimum 2048-bit key size
- Generate new certificate if current one doesn't meet requirements

## Payload Issues

### JSON Serialization Problems

**Problem:** Payload contains non-serializable data

**Diagnosis:**
```typescript
function diagnoseSerializationIssues(payload) {
  try {
    const serialized = JSON.stringify(payload);
    console.log('✓ Payload serializes successfully');
    console.log(`Serialized size: ${serialized.length} characters`);
    return true;
  } catch (error) {
    console.error('✗ Serialization failed:', error.message);
    
    // Check for common issues
    if (error.message.includes('circular')) {
      console.error('Issue: Circular reference detected');
      console.log('Solution: Use handleCircularReferences() function');
    }
    
    return false;
  }
}
```

**Solutions:**
- Remove circular references using `handleCircularReferences()`
- Convert functions to strings or remove them
- Handle Date objects properly
- Remove undefined values

### Large Payload Optimization

**Problem:** Payload exceeds encryption size limits

**Optimization Strategies:**
```typescript
function optimizePayload(payload) {
  // 1. Remove unnecessary fields
  const essential = {
    id: payload.id,
    action: payload.action,
    timestamp: payload.timestamp
    // Only include essential fields
  };
  
  // 2. Shorten field names
  const shortened = {
    i: payload.id,        // id -> i
    a: payload.action,    // action -> a
    t: payload.timestamp  // timestamp -> t
  };
  
  // 3. Use more compact data types
  const compact = {
    ...payload,
    timestamp: Math.floor(Date.now() / 1000) // Use Unix timestamp
  };
  
  return compact;
}
```

### Payload Safety Issues

**Problem:** Payload contains potentially dangerous content

**Check:**
```typescript
import { validatePayloadSafety } from './utils/encryption';

function checkPayloadSafety(payload) {
  const safety = validatePayloadSafety(payload);
  
  if (!safety.isSafe) {
    console.warn('Payload safety issues:');
    safety.issues.forEach(issue => console.warn(`  - ${issue}`));
  }
  
  return safety.isSafe;
}
```

**Solutions:**
- Remove script tags and JavaScript code
- Limit payload nesting depth
- Reduce overall payload size
- Sanitize string inputs

## Performance Problems

### Slow Encryption Performance

**Problem:** Encryption takes too long in load tests

**Diagnosis:**
```typescript
import { Trend } from 'k6/metrics';

const encryptionTime = new Trend('encryption_duration');

export default async function() {
  const start = Date.now();
  const encrypted = await payloadEncryptionFactory(payload, certificate);
  const duration = Date.now() - start;
  
  encryptionTime.add(duration);
  
  if (duration > 1000) {
    console.warn(`Slow encryption: ${duration}ms`);
  }
}
```

**Solutions:**
- Use smaller payloads
- Cache parsed certificates when possible
- Consider using smaller RSA keys (if security allows)
- Optimize payload structure

### Memory Usage Issues

**Problem:** High memory consumption during encryption

**Monitoring:**
```typescript
function monitorMemoryUsage() {
  // K6 doesn't have direct memory monitoring, but you can:
  
  // 1. Monitor payload sizes
  const payloadSize = JSON.stringify(payload).length;
  console.log(`Payload size: ${payloadSize} bytes`);
  
  // 2. Limit concurrent operations
  if (__VU > 50) {
    console.warn('High VU count may cause memory issues');
  }
  
  // 3. Clean up variables
  payload = null; // Help garbage collection
}
```

**Solutions:**
- Reduce payload sizes
- Limit concurrent virtual users
- Clean up variables after use
- Use streaming for large datasets

## K6 Environment Issues

### Module Import Problems

**Problem:** Cannot import encryption utility in K6

**Check Build Configuration:**
```javascript
// rollup.config.js
export default {
  input: 'src/your-script.ts',
  output: {
    file: 'dist/your-script.js',
    format: 'es'
  },
  external: ['k6', 'k6/http', 'k6/metrics'],
  // Ensure encryption utility is included
};
```

**Solutions:**
- Verify rollup configuration includes encryption utility
- Check import paths are correct
- Ensure all dependencies are bundled
- Test import in simple script first

### Runtime Compatibility Issues

**Problem:** Functions don't work in K6 runtime

**Check K6 Compatibility:**
```typescript
// Test K6 environment detection
if (typeof __VU !== 'undefined') {
  console.log('Running in K6 environment');
} else {
  console.log('Running in Node.js environment');
}

// Test required APIs
if (typeof TextEncoder === 'undefined') {
  console.error('TextEncoder not available');
}

if (typeof atob === 'undefined') {
  console.error('atob not available');
}
```

**Solutions:**
- Use K6-compatible APIs only
- Avoid Node.js-specific functions
- Test in actual K6 environment
- Use polyfills if necessary

### TextEncoder/TextDecoder Errors

**Problem:** `TextEncoder is not defined` error during validation

**Symptoms:**
```
ERRO[0041] Input validation failed: {"code":"SERIALIZATION_FAILED","message":"Payload must be JSON serializable","details":{"originalError":"TextEncoder is not defined"}}
```

**Cause:** The encryption utility was using `TextEncoder` which is not available in all environments.

**Solution:** 
✅ **Fixed in latest version!** The utility now uses K6-compatible `stringToUint8Array` instead of `TextEncoder`. Update to the latest version and the error should be resolved.

**Verification:**
```typescript
// Test that validation works without TextEncoder
import { validatePayloadInput } from './utils/encryption';

const result = validatePayloadInput({ test: 'data' });
console.log('Validation works:', result.success);
```

## Debugging Techniques

### Enable Detailed Logging

```typescript
// Set up comprehensive logging
function debugEncryption(payload, certificate) {
  console.log('=== Encryption Debug Info ===');
  
  // 1. Input validation
  console.log('Payload type:', typeof payload);
  console.log('Payload size:', JSON.stringify(payload).length, 'chars');
  console.log('Certificate length:', certificate.length, 'chars');
  
  // 2. Certificate analysis
  const certInfo = parsePEMCertificate(certificate);
  console.log('Certificate valid:', certInfo.valid);
  console.log('Key size:', certInfo.keySize);
  console.log('Algorithm:', certInfo.algorithm);
  console.log('Expires:', certInfo.expirationDate);
  
  // 3. Size validation
  const payloadBytes = new TextEncoder().encode(JSON.stringify(payload)).length;
  const maxSize = Math.floor((certInfo.keySize || 2048) / 8) - 42;
  console.log('Payload bytes:', payloadBytes);
  console.log('Max allowed:', maxSize);
  console.log('Size valid:', payloadBytes <= maxSize);
  
  console.log('=== End Debug Info ===');
}
```

### Step-by-Step Validation

```typescript
async function stepByStepValidation(payload, certificate) {
  console.log('Step 1: Input validation');
  const inputValidation = validateEncryptionInputs(payload, certificate);
  if (!inputValidation.success) {
    console.error('Input validation failed:', inputValidation.error);
    return false;
  }
  console.log('✓ Input validation passed');
  
  console.log('Step 2: Certificate parsing');
  const certInfo = parsePEMCertificate(certificate);
  if (!certInfo.valid) {
    console.error('Certificate parsing failed');
    return false;
  }
  console.log('✓ Certificate parsing passed');
  
  console.log('Step 3: Size validation');
  const payloadString = JSON.stringify(payload);
  const payloadSize = new TextEncoder().encode(payloadString).length;
  if (!validatePayloadSize(payloadSize, certInfo.keySize || 2048)) {
    console.error('Payload size validation failed');
    return false;
  }
  console.log('✓ Size validation passed');
  
  console.log('Step 4: Encryption');
  const encrypted = encryptWithRSAOAEP(payloadString, certInfo.publicKey);
  if (!encrypted) {
    console.error('Encryption failed');
    return false;
  }
  console.log('✓ Encryption successful');
  
  return true;
}
```

### Performance Profiling

```typescript
function profileEncryption(payload, certificate) {
  const profile = {
    start: Date.now(),
    validation: 0,
    parsing: 0,
    encryption: 0,
    total: 0
  };
  
  // Validation timing
  const validationStart = Date.now();
  const validation = validateEncryptionInputs(payload, certificate);
  profile.validation = Date.now() - validationStart;
  
  if (!validation.success) return profile;
  
  // Parsing timing
  const parsingStart = Date.now();
  const certInfo = parsePEMCertificate(certificate);
  profile.parsing = Date.now() - parsingStart;
  
  if (!certInfo.valid) return profile;
  
  // Encryption timing
  const encryptionStart = Date.now();
  const encrypted = encryptWithRSAOAEP(JSON.stringify(payload), certInfo.publicKey);
  profile.encryption = Date.now() - encryptionStart;
  
  profile.total = Date.now() - profile.start;
  
  console.log('Performance Profile:');
  console.log(`  Validation: ${profile.validation}ms`);
  console.log(`  Parsing: ${profile.parsing}ms`);
  console.log(`  Encryption: ${profile.encryption}ms`);
  console.log(`  Total: ${profile.total}ms`);
  
  return profile;
}
```

## Ignore Certificate Expiration (Testing Feature)

### When to Use

The `ignoreExpiration` parameter allows you to bypass certificate expiration validation. Use this feature when:

- **System Date Issues**: Your system date is incorrect (like being set to the wrong year)
- **Testing Scenarios**: You need to test with expired certificates
- **Development**: Certificate renewal is not feasible in development environment

### How to Use

```typescript
// Basic encryption ignoring expiration
const encrypted = await payloadEncryptionFactory(payload, certificate, true);

// Enhanced encryption ignoring expiration
const result = await payloadEncryptionFactoryWithErrors(payload, certificate, true);

// Certificate parsing ignoring expiration
const certInfo = parsePEMCertificate(certificate, true);
```

### Example: System Date Issue

```typescript
// Your system shows: September 2025 (incorrect)
// Certificate expires: May 2025
// Without ignoreExpiration: FAILS (certificate appears expired)
// With ignoreExpiration: WORKS (bypasses date check)

const result = await payloadEncryptionFactoryWithErrors(payload, certificate, true);
if (result.success) {
  console.log('Encryption successful despite system date issue');
}
```

### Security Warning

⚠️ **NEVER use `ignoreExpiration = true` in production code!** This feature is only for testing and development. Production systems should always validate certificate expiration for security.

## FAQ

### Q: Why does encryption return null without error messages?

**A:** The basic `payloadEncryptionFactory` function returns null on any failure to maintain backward compatibility. Use `payloadEncryptionFactoryWithErrors` for detailed error information.

### Q: What's the maximum payload size I can encrypt?

**A:** It depends on your RSA key size:
- RSA-2048: ~214 bytes
- RSA-3072: ~342 bytes  
- RSA-4096: ~470 bytes

### Q: Can I use certificates with different algorithms?

**A:** No, only RSA certificates are supported. The utility doesn't support ECDSA, DSA, or other algorithms.

### Q: Why is encryption slow in my load test?

**A:** RSA encryption is computationally expensive. Consider:
- Using smaller payloads
- Reducing the number of concurrent operations
- Caching parsed certificates
- Using more powerful hardware

### Q: How do I handle certificate expiration in production?

**A:** Implement certificate monitoring:
- Check expiration dates regularly
- Set up alerts for certificates expiring soon
- Have a certificate renewal process
- Test with new certificates before deployment

### Q: Can I encrypt binary data?

**A:** The utility is designed for JSON payloads. For binary data, you'd need to base64-encode it first, but this increases size significantly.

### Q: Is the encryption output deterministic?

**A:** No, RSA-OAEP uses random padding, so the same input will produce different encrypted outputs each time. This is a security feature.

### Q: How do I verify my encrypted data is correct?

**A:** You can't decrypt within K6, but you can:
- Verify the output is valid base64
- Check the output length is reasonable
- Test decryption in your backend system
- Use integration tests to verify end-to-end functionality