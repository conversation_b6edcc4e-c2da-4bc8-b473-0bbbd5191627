# K6 Encryption Utility Documentation

Complete documentation for the K6-compatible encryption utility that provides RSA encryption functionality for JSON payloads using X.509 certificates.

## 📚 Documentation Index

### 🚀 Getting Started
- **[Quick Start Guide](../src/utils/README.md)** - Basic setup and usage
- **[Basic Example](../examples/basic-encryption-example.js)** - Simple K6 script example
- **[Advanced Example](../examples/advanced-encryption-example.js)** - Advanced usage with error handling

### 📖 Reference Documentation
- **[API Reference](encryption-utility-api.md)** - Complete API documentation
- **[Usage Examples](encryption-utility-examples.md)** - Comprehensive usage examples
- **[Troubleshooting Guide](encryption-utility-troubleshooting.md)** - Common issues and solutions

### 🔧 Technical Details
- **[Requirements Document](../.kiro/specs/k6-encryption-utility/requirements.md)** - Feature requirements
- **[Design Document](../.kiro/specs/k6-encryption-utility/design.md)** - Technical design and architecture
- **[Implementation Tasks](../.kiro/specs/k6-encryption-utility/tasks.md)** - Development task list

## 🎯 Quick Navigation

### For New Users
1. Start with the [Quick Start Guide](../src/utils/README.md)
2. Try the [Basic Example](../examples/basic-encryption-example.js)
3. Read the [API Reference](encryption-utility-api.md) for detailed function documentation

### For Advanced Users
1. Review [Usage Examples](encryption-utility-examples.md) for complex scenarios
2. Check [Advanced Example](../examples/advanced-encryption-example.js) for production patterns
3. Use [Troubleshooting Guide](encryption-utility-troubleshooting.md) when issues arise

### For Developers
1. Read the [Design Document](../.kiro/specs/k6-encryption-utility/design.md) for architecture
2. Check [Requirements Document](../.kiro/specs/k6-encryption-utility/requirements.md) for specifications
3. Review [Implementation Tasks](../.kiro/specs/k6-encryption-utility/tasks.md) for development progress

## 📋 Feature Overview

### Core Functionality
- ✅ **RSA Encryption**: RSA-OAEP encryption with X.509 certificates
- ✅ **K6 Compatible**: Works within K6's JavaScript runtime (v1.1.1)
- ✅ **Certificate Validation**: Comprehensive X.509 certificate validation
- ✅ **Error Handling**: Detailed error reporting and handling
- ✅ **Input Validation**: Comprehensive payload and certificate validation
- ✅ **Performance Optimized**: Efficient for load testing scenarios

### Supported Features
- **Certificate Formats**: PEM-formatted X.509 certificates
- **Key Types**: RSA keys (2048, 3072, 4096 bits)
- **Payload Types**: JSON-serializable objects
- **Output Format**: Base64-encoded encrypted data
- **Error Types**: Structured error codes and messages

## 🔍 Quick Reference

### Main Functions

```typescript
// Basic encryption
const encrypted = await payloadEncryptionFactory(payload, certificate);

// Enhanced encryption with error details
const result = await payloadEncryptionFactoryWithErrors(payload, certificate);

// Input validation
const validation = validateEncryptionInputs(payload, certificate);

// Certificate parsing
const certInfo = parsePEMCertificate(certificate);
```

### Common Use Cases

| Use Case | Documentation | Example |
|----------|---------------|---------|
| Basic encryption in K6 | [Basic Example](../examples/basic-encryption-example.js) | Simple load test |
| Production load testing | [Advanced Example](../examples/advanced-encryption-example.js) | Error handling + metrics |
| API integration | [Usage Examples](encryption-utility-examples.md#integration-with-k6-http-requests) | Encrypted API calls |
| Error handling | [API Reference](encryption-utility-api.md#error-handling) | Detailed error management |
| Performance testing | [Usage Examples](encryption-utility-examples.md#performance-testing) | Benchmarking encryption |
| Troubleshooting | [Troubleshooting Guide](encryption-utility-troubleshooting.md) | Common issues |

## 🚨 Common Issues

| Issue | Quick Fix | Documentation |
|-------|-----------|---------------|
| Encryption returns null | Use `payloadEncryptionFactoryWithErrors` | [Troubleshooting](encryption-utility-troubleshooting.md#encryption-returns-null) |
| Certificate expired | Check certificate validity | [Certificate Problems](encryption-utility-troubleshooting.md#certificate-problems) |
| Payload too large | Reduce payload size or use larger key | [Payload Issues](encryption-utility-troubleshooting.md#payload-issues) |
| Import errors in K6 | Check rollup configuration | [K6 Environment Issues](encryption-utility-troubleshooting.md#k6-environment-issues) |
| Slow performance | Optimize payload size | [Performance Problems](encryption-utility-troubleshooting.md#performance-problems) |

## 📊 Payload Size Limits

| RSA Key Size | Max Payload Size | Recommended Use |
|--------------|------------------|-----------------|
| 2048 bits | ~214 bytes | Small payloads (credentials, tokens) |
| 3072 bits | ~342 bytes | Medium payloads (user data, transactions) |
| 4096 bits | ~470 bytes | Large payloads (detailed records) |

## 🔐 Security Features

- **RSA-OAEP Padding**: Provides semantic security
- **Certificate Validation**: Validates format, expiration, and key requirements
- **Input Sanitization**: Prevents injection attacks and validates data safety
- **Error Security**: Structured errors without sensitive data exposure
- **Size Validation**: Enforces payload size limits for security

## 🎯 Performance Guidelines

### Optimization Tips
- Keep payloads under size limits for your key size
- Use certificate caching when possible
- Monitor encryption duration in load tests
- Consider payload structure optimization
- Use appropriate virtual user counts

### Monitoring Metrics
- `encryption_success_rate` - Success rate of encryption operations
- `encryption_duration_ms` - Time taken for encryption
- `payload_size_bytes` - Size of payloads being encrypted
- `certificate_errors` - Certificate-related error count
- `payload_errors` - Payload-related error count

## 🛠️ Development

### Building and Testing
```bash
# Build the project
npm run build

# Run tests
npm test

# Run K6 examples
k6 run examples/basic-encryption-example.js
```

### File Structure
```
src/utils/
├── encryption.ts              # Main encryption functions
├── certificate-validation.ts  # Certificate validation
├── k6-utils.ts               # K6-compatible utilities
├── index.ts                  # Main exports
└── README.md                 # Quick start guide

docs/
├── encryption-utility-api.md           # API reference
├── encryption-utility-examples.md      # Usage examples
├── encryption-utility-troubleshooting.md # Troubleshooting
└── README.md                           # This file

examples/
├── basic-encryption-example.js    # Basic K6 example
└── advanced-encryption-example.js # Advanced K6 example
```

## 📞 Support

1. **Documentation**: Check the relevant documentation section above
2. **Examples**: Try the provided examples to understand usage patterns
3. **Troubleshooting**: Use the troubleshooting guide for common issues
4. **API Reference**: Consult the API documentation for detailed function information

## 🔄 Version Information

- **K6 Compatibility**: v1.1.1+
- **Node.js**: Not required (K6 runtime only)
- **Dependencies**: jsrsasign library
- **TypeScript**: Full TypeScript support with type definitions