/**
 * Example using your specific certificate with ignoreExpiration
 */

import { payloadEncryptionFactory, payloadEncryptionFactoryWithErrors } from './src/utils/encryption';

// Your certificate
const YOUR_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

async function demonstrateUsage() {
  console.log('🔐 K6 Encryption Utility - Your Certificate Example\n');

  // Your payload
  const payload = {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456"
  };

  console.log('📋 Payload to encrypt:', JSON.stringify(payload, null, 2));
  console.log('');

  // Method 1: Basic encryption with ignoreExpiration
  console.log('🔧 Method 1: Basic encryption (ignoring expiration)');
  const encrypted = await payloadEncryptionFactory(payload, YOUR_CERTIFICATE, true);
  
  if (encrypted) {
    console.log('✅ SUCCESS!');
    console.log(`📏 Encrypted data length: ${encrypted.length} characters`);
    console.log(`🔤 Encrypted data: ${encrypted}`);
  } else {
    console.log('❌ FAILED');
  }

  console.log('\n' + '='.repeat(60) + '\n');

  // Method 2: Enhanced encryption with detailed error handling
  console.log('🔧 Method 2: Enhanced encryption with error details');
  const result = await payloadEncryptionFactoryWithErrors(payload, YOUR_CERTIFICATE, true);
  
  if (result.success) {
    console.log('✅ SUCCESS!');
    console.log(`📏 Encrypted data length: ${result.data?.length} characters`);
    console.log(`🔤 Encrypted data: ${result.data}`);
  } else {
    console.log('❌ FAILED');
    console.log(`🚨 Error: ${result.error?.code} - ${result.error?.message}`);
  }

  console.log('\n' + '='.repeat(60) + '\n');

  // Usage in K6 script
  console.log('📝 Usage in your K6 script:');
  console.log('```javascript');
  console.log('import { payloadEncryptionFactory } from "./dist/utils/encryption.js";');
  console.log('');
  console.log('export default async function () {');
  console.log('  const payload = {');
  console.log('    pin: "1234",');
  console.log('    cardId: "****************",');
  console.log('    cardType: "VISA"');
  console.log('  };');
  console.log('');
  console.log('  const certificate = `your-certificate-here`;');
  console.log('');
  console.log('  // Ignore expiration (third parameter = true)');
  console.log('  const encrypted = await payloadEncryptionFactory(payload, certificate, true);');
  console.log('');
  console.log('  if (encrypted) {');
  console.log('    console.log("Encryption successful:", encrypted);');
  console.log('  } else {');
  console.log('    console.log("Encryption failed");');
  console.log('  }');
  console.log('}');
  console.log('```');

  console.log('\n💡 Key Points:');
  console.log('• The third parameter `true` ignores certificate expiration');
  console.log('• Your certificate is valid but appears expired due to system date');
  console.log('• Use `ignoreExpiration = true` for testing only');
  console.log('• In production, fix the system date or get a new certificate');
}

demonstrateUsage().catch(console.error);