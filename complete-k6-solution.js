/**
 * Complete K6 Solution with Auto-Certificate Formatting
 * 
 * This script automatically formats your certificate and encrypts your payload
 * Use this as your complete K6 script
 */

import { payloadEncryptionFactory } from "./dist/utils/encryption.js";
import { check } from 'k6';

// Auto-format certificate function (add this to your K6 script)
function autoFormatCertificate(certString) {
  if (!certString || typeof certString !== 'string') {
    throw new Error('Certificate must be a non-empty string');
  }

  // Remove all whitespace and line breaks
  const cleanCert = certString.replace(/\s/g, "");
  
  // Check for BEGIN and END markers
  const beginMarker = "-----BEGINCERTIFICATE-----";
  const endMarker = "-----ENDCERTIFICATE-----";
  
  const beginIndex = cleanCert.indexOf(beginMarker);
  const endIndex = cleanCert.indexOf(endMarker);
  
  if (beginIndex === -1 || endIndex === -1) {
    throw new Error('Invalid certificate: missing BEGIN CERTIFICATE or END CERTIFICATE markers');
  }
  
  // Extract the base64 content between markers
  const certContent = cleanCert.substring(beginIndex + beginMarker.length, endIndex);
  
  if (!certContent || certContent.length === 0) {
    throw new Error('Invalid certificate: no content between markers');
  }
  
  // Split into 64-character lines (standard PEM format)
  const lines = [];
  for (let i = 0; i < certContent.length; i += 64) {
    lines.push(certContent.substring(i, i + 64));
  }
  
  // Reconstruct with proper formatting
  return `-----BEGIN CERTIFICATE-----\n${lines.join('\n')}\n-----END CERTIFICATE-----`;
}

// Your certificate (as it appears in your logs - all on one line)
const RAW_CERTIFICATE = `-----BEGIN CERTIFICATE-----MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQELBQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcMDVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQxFTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMyMDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlVbml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7VXNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvRxZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsIFeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNNN/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQNrrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWyAcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvGSCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZI/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/QmKZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9RsxBoI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzCwW5lyO+/qcFe-----END CERTIFICATE-----`;

// K6 test configuration
export const options = {
  vus: 1,
  duration: '30s',
  thresholds: {
    checks: ['rate>0.9'], // 90% of checks should pass
  },
};

export default async function () {
  console.log('🔐 Starting K6 Encryption Test');

  // Your payload
  const payload = {
    "pin": "1234",
    "cardId": "aFBqZHZrZkVhemR3aVVTc3YzejFmY0RJdkoyOXdwY3JabjJYdEFjSlpZL3BHLzZ0cnBUdGhhekc2dUVQdzJFKzVvdEcra2EwM3hrcHpHQTJQRWFPaEJkZncyUFRITi9pZ0hOcU9YMWRKeGs9",
    "cardType": "DebitCard",
    "otp": "123456"
  };

  console.log("Payload Data To Encrypt", payload);

  try {
    // Step 1: Auto-format the certificate
    console.log("📋 Auto-formatting certificate...");
    const formattedCertificate = autoFormatCertificate(RAW_CERTIFICATE);
    console.log("✅ Certificate formatted successfully");

    // Step 2: Encrypt with formatted certificate and ignoreExpiration = true
    console.log("🔐 Encrypting payload...");
    const encrypted = await payloadEncryptionFactory(payload, formattedCertificate, true);

    // Step 3: Validate results
    const encryptionChecks = check(encrypted, {
      'encryption successful': (result) => result !== null,
      'result is string': (result) => typeof result === 'string',
      'result has content': (result) => result && result.length > 0,
      'result is valid base64': (result) => {
        if (!result) return false;
        try {
          atob(result);
          return true;
        } catch {
          return false;
        }
      }
    });

    if (encrypted) {
      console.log("🎉 SUCCESS! Encryption completed");
      console.log(`📏 Encrypted data length: ${encrypted.length} characters`);
      console.log(`🔤 Encrypted data: ${encrypted}`);
      
      // You can now use the encrypted data in your API calls
      // Example:
      // const response = http.post('your-api-endpoint', JSON.stringify({
      //   encryptedData: encrypted
      // }));
      
      return encrypted;
    } else {
      console.log("❌ Encryption failed - returned null");
      return null;
    }

  } catch (error) {
    console.error("💥 Error during encryption process:", error);
    
    check(null, {
      'no errors occurred': () => false
    });
    
    return null;
  }
}

// Setup function (runs once)
export function setup() {
  console.log('=== K6 Encryption Test Setup ===');
  console.log('✅ Auto-certificate formatting enabled');
  console.log('✅ Certificate expiration ignored (ignoreExpiration = true)');
  console.log('✅ TextEncoder compatibility fixed');
  console.log('=== Ready to start ===');
}

// Teardown function (runs once after test)
export function teardown(data) {
  console.log('=== K6 Encryption Test Complete ===');
}