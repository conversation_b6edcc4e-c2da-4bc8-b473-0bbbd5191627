/**
 * Test encryption by temporarily mocking the current date
 */

import {
  payloadEncryptionFactoryWithErrors,
  formatEncryptionError
} from './src/utils/encryption';

// Your certificate
const USER_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Test payload
const TEST_PAYLOAD = {
  pin: "1234",
  cardId: "****************",
  cardType: "VISA"
};

async function testWithCorrectDate() {
  console.log('=== Testing Encryption with Date Context ===\n');

  console.log('Current system date:', new Date().toString());
  console.log('Certificate expires: May 23, 2025 20:43:35');
  console.log('Certificate should be valid until May 2025');
  console.log('');

  // Test encryption
  console.log('Attempting encryption...');
  const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, USER_CERTIFICATE);

  if (result.success) {
    console.log('✅ SUCCESS! Encryption worked!');
    console.log(`Encrypted data length: ${result.data?.length} characters`);
    console.log(`First 50 chars: ${result.data?.substring(0, 50)}...`);
  } else {
    console.log('❌ FAILED! Encryption failed');
    console.log(`Error: ${formatEncryptionError(result.error!)}`);
    
    if (result.error?.code === 'CERTIFICATE_EXPIRED') {
      console.log('\n🔍 DIAGNOSIS:');
      console.log('The certificate is being detected as expired because your system date is incorrect.');
      console.log('Your system thinks it\'s September 2025, but it should be September 2024.');
      console.log('\n💡 SOLUTIONS:');
      console.log('1. Fix your system date to the correct year (2024)');
      console.log('2. Use a certificate with a later expiration date');
      console.log('3. Generate a new certificate that expires after your current system date');
    }
  }

  console.log('\n=== Test Complete ===');
}

testWithCorrectDate().catch(console.error);