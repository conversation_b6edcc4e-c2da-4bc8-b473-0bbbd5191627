/**
 * Debug script to diagnose certificate issues
 */

import {
  payloadEncryptionFactoryWithErrors,
  validateCertificateInput,
  parsePEMCertificate,
  formatEncryptionError,
  comprehensiveValidation
} from './src/utils/encryption';

import { validateCertificateFormat } from './src/utils/certificate-validation';

// Your certificate
const USER_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDxTCCAq2gAwIBAgIURstt+QUw8lIPlge0psbVvOBvQLUwDQYJKoZIhvcNAQEL
BQAwcjELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNhbGlmb3JuaWExFjAUBgNVBAcM
DVNhbiBGcmFuY2lzY28xDjAMBgNVBAoMBU15T3JnMQ8wDQYDVQQLDAZNeVVuaXQx
FTATBgNVBAMMDG15ZG9tYWluLmNvbTAeFw0yNDA1MjMyMDQzMzVaFw0yNTA1MjMy
MDQzMzVaMHIxCzAJBgNVBAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYD
VQQHDA1TYW4gRnJhbmNpc2NvMQ4wDAYDVQQKDAVNeU9yZzEPMA0GA1UECwwGTXlV
bml0MRUwEwYDVQQDDAxteWRvbWFpbi5jb20wggEiMA0GCSqGSIb3DQEBAQUAA4IB
DwAwggEKAoIBAQCqOkVjPgZX2eDTtqGoMSwmrNwmd7XlZAOyfA/o69a1YFp/Zz7V
XNBYHoq75oxzztC68esyjhe0tVhQYF5kfzAglEIXQGrgQPdwQeE7QDUWZw+mAvNR
2CuhIlbHFpSVE4w53GGRubXWIgRSzYAAW1xMB+208Taa2x/5ACITbolZiPwPtDvR
xZi1Ri1LQ5rvJ00W4VFwPI/hvS3hzNlj2Mjb8Ypovswpu0dzan8VpYGIQxTjqOsI
FeH0syT43PveGSLFPSkG+iQxz0Kl6f+nH3TJOuJa4NJBHot4Yr8GnkUMlt+ztmNN
N/aoqt+zlTKu80MRiVDr3kW+HFCJy0ohkI29AgMBAAGjUzBRMB0GA1UdDgQWBBQN
rrKJWgyJdZTqsAdpYOWyAcHenDAfBgNVHSMEGDAWgBQNrrKJWgyJdZTqsAdpYOWy
AcHenDAPBgNVHRMBAf8EBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQBtsMglVmvG
SCOLrJ/siEJdMNMAIqELuQK3FDmKoURHlxR0E80BvZOu1rb0pVpkisA9CMvO8IOZ
I/STCUCbcYFWokZ6Rq709P1gSdAWHCE4zMPB0Ne5ZfljsA0avDsNSgb7fePYV/Qm
KZ+Go8HBd8KQEktfY+2YfMq/7CxEFFln1YBV883BcVhYLxlJlTk6OQn6GeZ9Rsxb
oI2mitu9SAO6SXGEK3SUwIZObUKvZgaCw+rK4R4tlxmr1f4XQO9hvYYzPzLUIrx3
YnfL2qRusLT3XXWfFGwwGZCCrmyqFvuckQuJqRd5ujoUlzWwUp5Bs9ERiXwBnNzC
wW5lyO+/qcFe
-----END CERTIFICATE-----`;

// Test payload
const TEST_PAYLOAD = {
  pin: "1234",
  cardId: "****************",
  cardType: "VISA"
};

async function diagnoseCertificateIssue() {
  console.log('=== Certificate Diagnosis ===\n');

  // Step 1: Basic certificate format validation
  console.log('1. Certificate Format Validation:');
  const formatValidation = validateCertificateFormat(USER_CERTIFICATE);
  console.log(`   Valid: ${formatValidation.isValid}`);
  if (!formatValidation.isValid) {
    console.log('   Errors:', formatValidation.errors);
    console.log('   Warnings:', formatValidation.warnings);
  } else {
    console.log('   ✓ Certificate format is valid');
  }

  // Step 2: Certificate input validation
  console.log('\n2. Certificate Input Validation:');
  const inputValidation = validateCertificateInput(USER_CERTIFICATE);
  console.log(`   Valid: ${inputValidation.success}`);
  if (!inputValidation.success) {
    console.log('   Error:', formatEncryptionError(inputValidation.error!));
  } else {
    console.log('   ✓ Certificate input validation passed');
  }

  // Step 3: Certificate parsing
  console.log('\n3. Certificate Parsing:');
  const certInfo = parsePEMCertificate(USER_CERTIFICATE);
  console.log(`   Valid: ${certInfo.valid}`);
  console.log(`   Key Size: ${certInfo.keySize} bits`);
  console.log(`   Algorithm: ${certInfo.algorithm}`);
  console.log(`   Issuer: ${certInfo.issuer}`);
  console.log(`   Subject: ${certInfo.subject}`);
  console.log(`   Expiration: ${certInfo.expirationDate}`);
  console.log(`   Is Expired: ${certInfo.isExpired}`);

  if (!certInfo.valid) {
    console.log('   ❌ Certificate parsing failed');
    if (certInfo.isExpired) {
      console.log('   Reason: Certificate has expired');
    }
  } else {
    console.log('   ✓ Certificate parsing successful');
  }

  // Step 4: Comprehensive validation
  console.log('\n4. Comprehensive Validation:');
  const comprehensive = comprehensiveValidation(TEST_PAYLOAD, USER_CERTIFICATE);
  console.log(`   Valid: ${comprehensive.isValid}`);
  console.log(`   Errors: ${comprehensive.errors.length}`);
  console.log(`   Warnings: ${comprehensive.warnings.length}`);
  console.log(`   Safety Issues: ${comprehensive.safetyIssues.length}`);

  if (comprehensive.errors.length > 0) {
    console.log('   Detailed Errors:');
    comprehensive.errors.forEach((error, index) => {
      console.log(`     ${index + 1}. ${formatEncryptionError(error)}`);
    });
  }

  if (comprehensive.warnings.length > 0) {
    console.log('   Warnings:');
    comprehensive.warnings.forEach((warning, index) => {
      console.log(`     ${index + 1}. ${warning}`);
    });
  }

  // Step 5: Attempt encryption with detailed errors
  console.log('\n5. Encryption Attempt:');
  try {
    const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, USER_CERTIFICATE);
    
    if (result.success) {
      console.log('   ✓ Encryption successful!');
      console.log(`   Encrypted data length: ${result.data?.length} characters`);
      console.log(`   First 50 chars: ${result.data?.substring(0, 50)}...`);
    } else {
      console.log('   ❌ Encryption failed');
      console.log(`   Error: ${formatEncryptionError(result.error!)}`);
      
      // Provide specific guidance
      switch (result.error?.code) {
        case 'CERTIFICATE_EXPIRED':
          console.log('   💡 Solution: The certificate has expired. You need a new, valid certificate.');
          break;
        case 'INVALID_CERTIFICATE':
          console.log('   💡 Solution: Check certificate format and ensure it\'s a valid PEM certificate.');
          break;
        case 'CERTIFICATE_PARSING_FAILED':
          console.log('   💡 Solution: The certificate cannot be parsed. Verify it\'s a valid X.509 certificate.');
          break;
        case 'PAYLOAD_TOO_LARGE':
          console.log('   💡 Solution: Reduce payload size or use a larger RSA key.');
          break;
        default:
          console.log('   💡 Check the troubleshooting guide for more information.');
      }
    }
  } catch (error) {
    console.log('   ❌ Exception during encryption:', error);
  }

  console.log('\n=== Diagnosis Complete ===');
}

// Run the diagnosis
diagnoseCertificateIssue().catch(console.error);