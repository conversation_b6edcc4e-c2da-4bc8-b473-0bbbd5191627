import {CustomHeaders} from "../../core/httpx";
import {check, group} from "k6";

export function listPrePaidCards(customHeaders: CustomHeaders) {
    return group("List PrePaid cards", () => {
        const res = api.get("/v1/cards/prepaid-cards", "listPrePaidCard", customHeaders);

        check(res, {
            "listPrePaidCard response is success": (r) => r.status === 200,
        });
        if (res.status !== 200) {
            throw new Error(
                `Use Case Failure - list PrePaid cards failed. - ${res.status} - ${res.body} - ${res.request.url}`,
            );
        }
        try {
            return res.json("data.cards");
        } catch (e: any) {
            throw new Error(
                `Use Case Failure - response json does not return cards. - ${e.message}`,
            );
        }
    });
}
export function getPrePaidCardDetails(cardID: any, customHeaders: CustomHeaders) {
    group("List Credit card Details", () => {

        const res = api.get(
            `/v1/cards/prepaid-cards/${cardID}`,
            "getPrePaidDetails",
            customHeaders,
        );
        check(res, {
            "PrePaid card details success": (r) => r.status === 200,
        });
    });
}

export function getCreditCardTransactions(cardId: any, customHeaders: CustomHeaders) {
    group("List PrePaid card Transaction", () => {

        const res = api.get(
            `/v1/cards/prepaid-cards/${cardId}/transactions`,
            "getPrePaidTransactions",
            customHeaders,
        );
        check(res, {
            "PrePaid card transactions success": (r) => r.status === 200,
        });
    });
}
