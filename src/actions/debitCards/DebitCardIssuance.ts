import { group, check } from 'k6';
import {CustomHeaders} from "../../core/httpx";


export function getGetEmbossingList(customHeaders: CustomHeaders) {
    return group("Get Embossing List", () => {
        const res = api.get("/v1/cards/debit-cards/issue/embossing-list", "get embossing list", customHeaders);
        check(res, { "Get Embossing List successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function getGetMeta(customHeaders: CustomHeaders) {
    return group("Get Meta", () => {
        const res = api.get("/v1/cards/debit-cards/issue/meta", "get meta", customHeaders);
        check(res, { "Get Meta successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postIssuanceSummary(body: any, customHeaders: CustomHeaders) {
    return group("Issuance Summary", () => {
        const res = api.post("/v1/cards/debit-cards/issue/summary", body, "issuance summary", customHeaders);
        check(res, { "Issuance Summary successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postIssuanceSubmit(body: any, customHeaders: CustomHeaders) {
    return group("Issuance submit", () => {
        const res = api.post("/v1/cards/debit-cards/issue/submit", body, "issuance submit", customHeaders);
        check(res, { "Issuance submit successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postIseligibile(body: any, customHeaders: CustomHeaders) {
    return group("isEligibile", () => {
        const res = api.post("/v1/cards/debit-cards/issue/submit", body, "iseligibile", customHeaders);
        check(res, { "isEligibile successful": (r) => r.status >= 200 && r.status < 300 });
        return res.json('data.enabled');
    });
}

