import { group, check } from "k6";
import {getNotifications, getSummaryTokenization, getUserDetails, listUserRequests} from "../users";
import { getProductList } from "../accounts";
import { listBeneficiaries } from "../beneficiaries";
import {
  listHomeTransfers,
  listRecentTransfers,
  listScheduledTransfers,
} from "../transfer";
import { listRelatedAccounts } from "../relatedAccounts";
import { CustomHeaders } from "../../core/httpx";

export function callHomepage(customHeaders: CustomHeaders) {
  getUserDetails(customHeaders);
  getProductList(customHeaders);
  listUserRequests(customHeaders);
  getNotifications(customHeaders);
  listRelatedAccounts(customHeaders);
  listHomeTransfers(customHeaders);
  // getSummaryTokenization(customHeaders)
}
