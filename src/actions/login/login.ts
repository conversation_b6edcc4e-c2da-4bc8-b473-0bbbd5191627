import {check, group, sleep,} from "k6";
import {RefinedResponse} from "k6/http";
import {CustomHeaders, loginWithBiometricPayload} from "../../core/httpx";
import {KEYUTIL, KJUR} from 'jsrsasign'
import encoding from "k6/encoding";
import {User} from "../../typings/globals";


interface BiometricEnrollPayload {
  deviceId: string;
  password: string;
  publicKey: string;
  otp: string;
}

interface BiometricEnrollResponse {
  success: boolean;
  message: string;
  cipherKey: string;
  kcUserId: string;
  accessToken: string;
  refreshToken: string;
}

interface EnrollmentData {
  response: BiometricEnrollResponse;
  deviceId: string;
  publicKey: string;
  privateKey: string;
}



interface KeyPair {
  publicKey: string;
  privateKey: string;
}
function generateKeys(): KeyPair {
  // Generate RSA key pair
  const keypair = KEYUTIL.generateKeypair('RSA', 2048);

  // Format public key (single-line base64)
  const publicKey = KEYUTIL.getPEM(keypair.pubKeyObj)
      .replace('-----B<PERSON>IN PUBLIC KEY-----', '')
      .replace('-----END PUBLIC KEY-----', '')
      .replace(/\n/g, '')
      .trim();

  // Get private key in PKCS#8 format
  const privateKey = KEYUTIL.getPEM(keypair.prvKeyObj, 'PKCS8PRV');

  return { publicKey, privateKey };
}

export  function login(user : User, deviceId: string): string | undefined {
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  sleep(1)

  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  increaseApiCallWithValidationRules()
  sleep(1)

  const randomNumber = Math.floor(Math.random() * 100) / 100
if (randomNumber < 0.7){
 return  biometricLogin(user , deviceId)
}else {
 return  normalLogin(user.username , user.password , deviceId)
}
}
function increaseApiCallWithValidationRules(){
  const validationResponse = idp.get(
      "/v3/auth/login/validation-rules",
      "validation-rules",
  );
  return validationResponse
}
function normalLogin(username: string, password: string, deviceId: string): string | undefined  {
  return  group("Login with username and password",  () => {
    const validationResponse = idp.get(
        "/v3/auth/login/validation-rules",
        "validation-rules",
    );
    idp.get(
        "/v3/auth/login/validation-rules",
        "validation-rules",
    );
    idp.get(
        "/v3/auth/login/validation-rules",
        "validation-rules",
    );


    check(validationResponse, {
      "validation rules response is success": (r) => r.status === 200,
    });

    const tutorialResponse = api.get(
        "/v1/cms/registration/tutorial",
        "tutorial",
    );
    check(tutorialResponse, {
      "tutorial response is success": (r) => r.status === 200,
    });

    let returnToken: string | undefined;
    let loginResponse: RefinedResponse<any>;

    loginResponse = idp.post(
        `/v3/auth/login`,
        {
          username,
          password: globalThis.passwordWasReset ? globalThis.resetPassword : password,
        },
        "login",
        {deviceId}
    );

    check(loginResponse, {
      "original login response is success": (r) => r.status === 200,
    });

    switch (loginResponse.status) {
      case 400:
        throw new Error(
            `Bad Request - response status code: ${loginResponse.status}`,
        );
      case 401:
        throw new Error(
            `Authentication Failure - response status code: ${loginResponse.status}`,
        );
      case 428:
        try {
          const token = loginResponse.json("token")?.toString();
          loginResponse = idp.post(
              `/v3/auth/login`,
              {
                username,
                password: globalThis.passwordWasReset ? globalThis.resetPassword : password,
              },
              "Confirm login",
              {token, deviceId},
          );
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return token. - ${e.message}`,
          );
        }
        break;
      case 417:

        const token = loginResponse.json("token")?.toString();
        if (token) {
          // Perform the password reset with the required headers and payload
          const resetPasswordPayload = {
            userName: username,
            newPassword: globalThis.resetPassword, // Define the new password here
            oldPassword: password,
          };

          const expiredResponse = idp.post(
              `/v3/auth/reset-expired-password`,
              resetPasswordPayload,
              "Reset expired password", // This is the label (string)
              {token}
          );

          check(expiredResponse, {
            "reset password response is success": (r) => r.status === 200,
          });

          // Log the reset password response message (optional)
          const resetMessage = expiredResponse.json("message")?.toString();
          console.log(resetMessage); // Should log "password reset successfully."

          if (resetMessage === "password reset successfully.") {
            globalThis.passwordWasReset = true;
            // After password reset, login with the new password
            const newPassword = resetPasswordPayload.newPassword;
            loginResponse = idp.post(
                `/v3/auth/login`,
                {
                  username,
                  password: newPassword, // Login with the new password
                },
                "login", // This is the label (string)
                {deviceId}
            );

            check(loginResponse, {
              "new password login response is success": (r) => r.status === 200,
            });

            returnToken = loginResponse
                ?.json("data.access_token")
                ?.toString();
          }
        }
        break;
      case 500:
        throw new Error(
            `Internal Server Error - response status code: ${loginResponse.status}`,
        );
      case 503:
        throw new Error(
            `Service Unavailable - response status code: ${loginResponse.status}`,
        );
      default:
        try {
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return access token. - ${e.message}`,
          );
        }
    }

    try {
      if (!returnToken) {
      } else {
        returnToken = handleTrustDevice(loginResponse, username, returnToken, deviceId);
        return returnToken;
      }
    } catch (e: any) {
      throw new Error(
          `Authentication Failure - response json does not return access token. - ${e.message}`,
      );
    }
  });

}

export function loginWithBiometric(payload : loginWithBiometricPayload): string | undefined {
  return group("Login with username and password", () => {
    const validationResponse = idp.get(
        "/v3/auth/login/validation-rules",
        "validation-rules",
    );
    check(validationResponse, {
      "validation rules response is success": (r) => r.status === 200,
    });

    const tutorialResponse = api.get(
        "/v1/cms/registration/tutorial",
        "tutorial",
    );
    check(tutorialResponse, {
      "tutorial response is success": (r) => r.status === 200,
    });

    let returnToken: string | undefined;
    let loginResponse: RefinedResponse<any>;
    const loginPayload = payload.data;

    loginResponse = idp.post(
        `/v3/auth/biometric/login`,
        loginPayload,
        "login",
        {
            "app-version":
                "MIICAQYJKoZIhvcNAQcDoIIB8jCCAe4CAQAxggGnMIIBowIBADCBijByMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZvcm5pYTEWMBQGA1UEBwwNU2FuIEZyYW5jaXNjbzEOMAwGA1UECgwFTXlPcmcxDzANBgNVBAsMBk15VW5pdDEVMBMGA1UEAwwMbXlkb21haW4uY29tAhRGy235BTDyUg+WB7SmxtW84G9AtTANBgkqhkiG9w0BAQEFAASCAQA+Sk26YUuRrcmdDk3499493cDiiKmvIabQIdCW7rnp8enT0ZZenYO3X3u41eFP+Tl400nN258/EMp4v2PjCD+gZRMDnc8y8ODI3v0emPt4Fr6J4EMYWhWZppfpF4XV4PhyKgxhjezoHv85WP+OwO9JPLrlOMlUrlHGHWlcMyKJSzShiz14DOvma+u86wYZ5Lrw+9O+wUffVt67/3VH2WDdlw4DuYAreqkRGd0ZiaLBwNr+jyrl7tgO4Efkrcxi6mcY3R7Sc0nm5gvE4ACDlTdASYHyKppExwTF5UAweVEeD4v5BvEObY34trqWynjdCypm4Ka4ykd220NG3Vsrb/FQMD4GCSqGSIb3DQEHATAdBglghkgBZQMEASoEEIE1avqTqsoQIp48H/+wMqqgEgQQzjtEMVtOC1ZEfoi8KBP3Mg==",
            "Content-Type": "application/json",
            login_auth_key: "dds",
            "device-id": payload.data.deviceId,
        }

    );

    check(loginResponse, {
      "original login response is success": (r) => r.status === 200,
    });

    switch (loginResponse.status) {
      case 400:
        throw new Error(
            `Bad Request - response status code: ${loginResponse.status}`,
        );
      case 401:
        throw new Error(
            `Authentication Failure - response status code: ${loginResponse.status}`,
        );
      case 428:
        try {
          const token = loginResponse.json("token")?.toString();
          loginResponse = idp.post(
              `/v3/auth/biometric/login`,
              loginPayload,
              "Confirm login",
              {token},
          );
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return token. - ${e.message}`,
          );
        }
        break;
      case 500:
        throw new Error(
            `Internal Server Error - response status code: ${loginResponse.status}`,
        );
      case 503:
        throw new Error(
            `Service Unavailable - response status code: ${loginResponse.status}`,
        );
      default:
        try {
          returnToken = loginResponse?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return access token. - ${e.message}`,
          );
        }
    }

    try {
      if (!returnToken) {
      } else {
        returnToken = handleTrustDevice(loginResponse, payload.user, returnToken, payload.data.deviceId);
        if (!returnToken) {
        } else {
          return returnToken;
        }
      }
    } catch (e: any) {
      throw new Error(
          `Authentication Failure - response json does not return access token. - ${e.message}`,
      );
    }
  });
}

function handleTrustDevice(loginResponse: RefinedResponse<any>, username: string, token: string | undefined, deviceId: string): string | undefined {
  const isTrusted = loginResponse?.json("data.isTrusted")?.valueOf() as boolean;
  if(!isTrusted){
    const trustResponseNoOtp = idp.post(
      `/v3/auth/trust`,
      {
        // deviceId: `${username}_deviceId`,
        deviceId : deviceId
      },
      "trustDevice",
      {token},
    );
    check(trustResponseNoOtp, {
      "trust response without OTP is success": (r) => r.status === 428,
    });

    const trustResponseOtp = idp.post(
      `/v3/auth/trust`,
      {
        deviceId: deviceId,
        otp: '555555'
      },
      "trustDevice",
      {token},
    );

    check(trustResponseOtp, {
      "trust response with OTP is success": (r) => r.status === 200,
    });

    return trustResponseOtp?.json("accessToken")?.toString();
  } else {
    return token;
  }
}

export  function payloadEncryptionKey(customHeaders: CustomHeaders) {
    return group("Payload Encryption Key", () => {
        const res = api.get("/v1/assets/payload-encryption-key", "Encryption Key", customHeaders);
        check(res, { "Encryption Key successful": (r) => r.status >= 200 && r.status < 300 });
        return res.json('encryptionKeyValue');
    });
}
////


// function biometricEnroll(token: string, deviceId: string ,key : KeyPair  ): EnrollmentData | null {
//   const payload: BiometricEnrollPayload = {
//     deviceId: deviceId,
//     password: 'password',
//     publicKey : key.publicKey,
//     otp: '1233',
//   };
//   const customHeader = {token , deviceId}
//
//   const response = idp.post('/v3/auth/biometric/enroll', payload, 'biometric enroll',customHeader);
//   check(response, {
//     'biometric enrollment successful': (r) => r.status === 200,
//   });
//   console.log("Biometric Enrollment :-" , response)
//
//
//   if (response.status === 200) {
//     const responseData :BiometricEnrollResponse = response.json() as unknown as BiometricEnrollResponse;
//     return {
//       response: responseData,
//       deviceId: deviceId,
//       publicKey : key.publicKey,
//       privateKey : key.privateKey
//     };
//   }
//   return null;
// }

function hexToBase64(hex: string): string {
  const byteArray = new Uint8Array(hex.length / 2);
  for (let i = 0; i < byteArray.length; i++) {
    byteArray[i] = parseInt(hex.substr(i * 2, 2), 16);
  }
  return encoding.b64encode(byteArray.buffer);
}

function signPayload(payload: string, privateKey: string): string {
  // 1. Parse the private key
  const key = KEYUTIL.getKey(privateKey);

  // 2. Create signature object
  const signature = new KJUR.crypto.Signature({
    alg: 'SHA256withRSA' // Matches Node.js SHA256 + RSA
  });

  // 3. Initialize with private key
  signature.init(key);

  // 4. Add payload
  signature.updateString(payload);

  // 5. Generate signature in hex format
  const hexSignature = signature.sign();

  // 6. Convert hex to base64
  return hexToBase64(hexSignature);
}
function biometricLogin(enrollmentData: User , deviceId : string ):string | undefined {
  return group("Biometric Login", () => {
    // const signaturePayload: string = JSON.stringify({
    //   cipherKey: enrollmentData.data.cipherKey,
    //   kcUserId: enrollmentData.data.kcUserId,
    //   deviceId: deviceId,
    // });


    const loginPayload = {
      deviceId: deviceId,
      payload : enrollmentData.data.payload,
      signature: enrollmentData.data.signature,
      cipherKey: enrollmentData.data.cipherKey,
      kcUserId: enrollmentData.data.kcUserId,
      // payload: `{"cipherKey":"${enrollmentData.data.cipherKey}","kcUserId":"${enrollmentData.data.kcUserId}","deviceId":"${enrollmentData.deviceId}"}`,

    };

    // const accessToken = enrollmentData.response.accessToken
    const customHeader = {deviceId}

    let tokenString: string | undefined;

    let response = idp.post('/v3/auth/biometric/login', loginPayload, "Biometric Login",customHeader);
    // console.log("Biometric Login :-" , response)

    switch (response.status) {
      case 400:
        throw new Error(
            `Bad Request - response status code: ${response.status}`,
        );
      case 401:
        throw new Error(
            `Authentication Failure - response status code: ${response.status}`,
        );
      case 428:
        try {
          const token = response.json("token")?.toString();
          response = idp.post(
              `/v3/auth/biometric/login`,
              loginPayload,
              "Confirm login",
              {  deviceId , token},
          );
          // console.log("Confirm Login :-" , response)

          tokenString = response?.json("data.access_token")?.toString()
          return  tokenString

        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return token. - ${e.message}`,
          );
        }
        break;
      case 500:
        throw new Error(
            `Internal Server Error - response status code: ${response.status}`,
        );
      case 503:
        throw new Error(
            `Service Unavailable - response status code: ${response.status}`,
        );
      default:
        try {
          tokenString = response?.json("data.access_token")?.toString();
        } catch (e: any) {
          throw new Error(
              `Authentication Failure - response json does not return access token. - ${e.message}`,
          );
        }
    }


    // Check the final response state after any retries
    check(response, {
      'biometric login successful': (r) => r.status === 200,
    });

    return tokenString;
  })
}


