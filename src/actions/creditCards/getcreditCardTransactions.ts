import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function getCreditCardDetails(account: any, customHeaders: CustomHeaders) {
 return  group("List Credit card Details", () => {
    const body = {
      id: account,
      cardType: "CreditCard",
    };
    const res = api.get(
      `/v1/cards/${account}?cardType=${body.cardType}`,
      "getCreditCardDetails",
        customHeaders,
    );
    check(res, {
      "Credit card details success": (r) => r.status === 200,
    });
    return res.json('data');
  });
}

export function getCreditCardTransactions(account: any, customHeaders: CustomHeaders) {
 return  group("List Credit card Transaction", () => {
    const body = {
      id: account,
      cardType: "CreditCard",
    };
    const res = api.get(
      `/v1/cards/${account}/transactions?cardType=${body.cardType}`,
      "getCreditCardTransactions",
      customHeaders,
    );
    check(res, {
      "Credit card transactions success": (r) => r.status === 200,
    });
  });
}

export function getCreditCardInstallment(account: any, customHeaders: CustomHeaders) {
  group("List Credit card Installment", () => {
    const body = {
      id: account,
      limit: 20,
      sort: "desc",
      fromAmount: 0,
      toAmount: 0,
      page: 1,
    };

    const res = api.post(
      "/v1/cards-per/epp/transactions",
      body,
      "getCreditCardInstallment",
      customHeaders,
    );

    check(res, {
      "Credit card Installment success": (r) => r.status === 201,
    });
  });
}
