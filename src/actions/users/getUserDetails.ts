import { check, group } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function getUserDetails(customHeaders: CustomHeaders): string | undefined {
  return group("Getting the user details", () => {
    return getUser(customHeaders);
  });
}

export function getNotifications(customHeaders: CustomHeaders) {
  group("Getting the user notifications", () => {
    getNotificationsCall(customHeaders);
  });
}
export function getSummaryTokenization(customHeader : CustomHeaders){
  group("Getting List of Tokenize Cards" , ()=> {
    const response = api.get("/v1/cards/summary/tokenization" , "getTokenizeCards" , customHeader);
    try {
      check(response , {
        "Tokenize Card List" : (response)=> response.status === 200
      })
    }catch (e : any) {
      throw new Error(
          `Use Case Failure - response json does not return tokenize Cards - ${e.message}`,
      );
    }
  })
}

function getUser(customHeaders: CustomHeaders): string | undefined {
  const res = api.get("/v1/users?skipCache=true", "getUserDetails", customHeaders);
  try {
    check(res, {
      "user response is success": (r) => r.status === 200 && email !== "",
    });
    if (res.status !== 200) {
      throw new Error(
        `Use Case Failure - get user failed - ${res.status} - ${res.body} - ${res.request.url}`,
      );
    }
    const email = res.json("data.emailAddress")?.toString() || "";
    return email;
  } catch (e: any) {
    throw new Error(
      `Use Case Failure - response json does not return email addtress. - ${e.message}`,
    );
  }
}

function getNotificationsCall(customHeaders: CustomHeaders) {
  const res = api.get("/v1/notifications?count=true", "notifications", customHeaders);

  check(res, {
    "notifications response is success": (r) => r.status === 200,
  });
}
