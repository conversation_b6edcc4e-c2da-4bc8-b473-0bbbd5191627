import { group, check } from "k6";
import { CustomHeaders } from "../../core/httpx";

export function getDepositList(customHeaders: CustomHeaders) {
  return group("Deposit List", () => {
    const res = api.get(
      "/v1/deposits?depositType=TD&sortParam=currency&sortDirection=asc&page=1",
      "deposit List",
      customHeaders,
    );
    check(res, {
      "Deposit List success": (r) => r.status === 200,
    });
    try {
      return res.json("data.depositsList");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return deposits list. - ${e.message}`,
      );
    }
  });
}

export function getDepositCatalogType(customHeaders: CustomHeaders) {
  return group("Deposit catalog type", () => {
    const res = api.get(
      "/v1/deposits/catalog?depositType=CD",
      "depositCatalogType",
      customHeaders,
    );
    check(res, {
      "Deposit catalog type success": (r) => r.status === 200,
    });
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositCatalogTenor(customHeaders: CustomHeaders) {
  return group("Deposit catalog tenor", () => {
    const res = api.get(
      "/v1/deposits/catalog?tenorRange=true&depositType=CD",
      "depositCatalogTenor",
      customHeaders,
    );
    check(res, {
      "Deposit catalog tenor success": (r) => r.status === 200,
    });
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositCatalogCurrency(customHeaders: CustomHeaders) {
  return group("Deposit catalog currency", () => {
    const res = api.get(
      "/v1/deposits/catalog?depositType=CD&currency=EGP",
      "depositCatalogCurrency",
      customHeaders,
    );
    check(res, {
      "Deposit catalog currency success": (r) => r.status === 200,
    });
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositDetails(depositID: any, customHeaders: CustomHeaders) {
  group("Deposit Details", () => {

    const res = api.get(
      `/v1/deposits/${depositID}`,
        "deposit Details",
        customHeaders,
    );
    check(res, {
      "Deposit Details success": (r) => r.status === 200,
    });
  });
}

export function getDepositUserEligibility(customHeaders: CustomHeaders) {
  return group("Deposit user eligibility", () => {
    const res = api.get(
      "/v1/deposits/user-eligibility",
      "deposit user eligibility",
      customHeaders,
    );
    check(res, {
      "Deposit user eligibility": (r) => r.status === 200,
    });
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function getDepositsTermsConditions(customHeaders: CustomHeaders) {
  return group("Deposit terms conditions", () => {
    const res = api.get(
      "/v1/deposits/terms-and-conditions",
      "deposit terms conditions",
      customHeaders,
    );
    check(res, {
      "Deposit terms and conditions success": (r) => r.status === 200,
    });
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
        `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}
export function validateIfDepositBreakable(depositID: any,customHeaders: CustomHeaders) {
  return group("Validate Id Deposit Breakable", () => {
    const res = api.get(
        `/v1/deposits/${depositID}/redeem/validate`,
        "Validate Deposit Break",
        customHeaders,
    );
    check(res, {
      "Validate Deposit Break": (r) => r.status === 200,
    });
    try {
      return res.json("data");
    } catch (e: any) {
      throw new Error(
          `Use Case Failure - response json does not return data. - ${e.message}`,
      );
    }
  });
}

export function redeemSummary(depositID: any,body : Object, customHeaders: CustomHeaders) {
  group("Redeem Summary", () => {

    const res = api.post(
        `/v1/deposits/${depositID}/redeem/summary`,
        body,
        "Redeem Summary",
        customHeaders,
    );
    check(res, {
      "Redeem Summary success": (r) => r.status === 200,
    });
  });
}

export function redeemSubmit(depositID: any,body : Object, customHeaders: CustomHeaders) {
  group("Redeem Submit", () => {

    const res = api.post(
        `/v1/deposits/${depositID}/redeem/submit`,
        body,
        "Redeem Submit",
        customHeaders,
    );
    check(res, {
      "Redeem Submit success": (r) => r.status === 200,
    });
  });
}

