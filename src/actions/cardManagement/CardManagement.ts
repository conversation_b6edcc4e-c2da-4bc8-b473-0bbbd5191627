import { group, check  } from 'k6';
import {CustomHeaders} from "../../core/httpx";
import { payloadEncryptionFactory } from "../../utils/encryption";
export function getCardsSummary(customHeaders: CustomHeaders) {
    return group("Cards Summary", () => {
        const res = api.get("/v1/cards/summary/tokenization", "cards summary", customHeaders);
        check(res, { "Cards Summary successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}



export function postStop(body: any,CardId : string,  customHeaders: CustomHeaders) {
    return group("Stop", () => {
        const res = api.post(`/v1/cards/${CardId}/stop`, body, "stop", customHeaders);
        check(res, { "Stop successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}


export function postFreezeCard(body: any,CardId :string, customHeaders: CustomHeaders) {
    return group("Freeze Card", () => {
        const res = api.post(`/v1/cards/${CardId}/freeze`, body, "freeze card", customHeaders);
        check(res, { "Freeze Card successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}
export function postUnfreezeCard(body: any,CardId :string, customHeaders: CustomHeaders) {
    return group("UnFreeze Card", () => {
        const res = api.post(`/v1/cards/${CardId}/unfreeze`, body, "unfreeze card", customHeaders);
        check(res, { "UnFreeze Card successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postActiveCards(body: any, customHeaders: CustomHeaders) {
    return group("Active Cards", () => {
        const res = api.post("/v1/cards/active", body, "active cards", customHeaders);
        check(res, { "Active Cards successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postResetPinAuthkey(body: any, customHeaders: CustomHeaders) {
    return group("Reset Pin authKey", () => {
        const res = api.post("/v1/cards/reset-pin", body, "reset pin authkey", customHeaders);
        check(res, { "Reset Pin authKey successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export async function postResetPinOtp(body: any, key: string, customHeaders: CustomHeaders) {
    return group("Reset Pin otp", async () => {
        // Use original RSA encryption method (working in K6)
        console.log("🔄 Using original RSA encryption method...");
        const encryptedPayload = await payloadEncryptionFactory(body, key, true);
        
        console.log("encryptedPayload result", encryptedPayload);
        // if (!encryptedPayload) {
        //     console.error("Failed to encrypt payload for reset pin OTP");
        //     // Return a failed response if encryption fails
        //     return {
        //         status: 500,
        //         json: () => ({ error: "Encryption failed" })
        //     };
        // }

        const res = api.post("/v1/cards/reset-pin", {"encryptedData": encryptedPayload}, "reset pin otp", customHeaders);
        console.log("Payload encrypted successfully" , res);
        check(res, { "Reset Pin otp successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postValidatePin(body: any, customHeaders: CustomHeaders) {
    return group("Validate Pin", () => {
        const res = api.post("/v1/cards/validate-pin", body, "validate pin", customHeaders);
        check(res, { "Validate Pin successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postSendOtp(body: any, customHeaders: CustomHeaders) {
    return group("Send OTP", () => {
        const res = api.post("/v1/cards/send-otp", body, "send otp", customHeaders);
        check(res, { "Send OTP successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}

export function postValidateOtp(body: any, customHeaders: CustomHeaders) {
    return group("Validate OTP", () => {
        const res = api.post("/v1/cards/validate-otp", body, "validate otp", customHeaders);
        check(res, { "Validate OTP successful": (r) => r.status >= 200 && r.status < 300 });
        return res;
    });
}


