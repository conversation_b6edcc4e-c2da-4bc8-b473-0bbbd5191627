/**
 * Integration tests for K6 encryption utility
 * Tests complete encryption workflow, K6 compatibility, and performance
 */

import { check, sleep } from 'k6';
import { Options } from 'k6/options';
import {
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  validateEncryptionInputs,
  comprehensiveValidation,
  EncryptionErrorType,
  formatEncryptionError
} from './utils/encryption';

// K6 test configuration
export const options: Options = {
  scenarios: {
    // Single user end-to-end test
    end_to_end: {
      executor: 'shared-iterations',
      vus: 1,
      iterations: 1,
      maxDuration: '30s',
      tags: { test_type: 'end_to_end' }
    },
    // Concurrent encryption performance test
    concurrent_encryption: {
      executor: 'constant-vus',
      vus: 10,
      duration: '30s',
      tags: { test_type: 'performance' }
    },
    // Load test with multiple payloads
    load_test: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: '10s', target: 5 },
        { duration: '20s', target: 10 },
        { duration: '10s', target: 0 }
      ],
      tags: { test_type: 'load' }
    }
  },
  thresholds: {
    'encryption_duration': ['p(95)<1000'], // 95% of encryptions should complete within 1s
    'encryption_success_rate': ['rate>0.95'], // 95% success rate
    'checks': ['rate>0.95'] // 95% of checks should pass
  }
};

// Test certificate (RSA 2048-bit, valid until 2030)
const TEST_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==
-----END CERTIFICATE-----`;

// Test payloads of different sizes and types
const TEST_PAYLOADS = {
  small: {
    pin: "1234",
    cardId: "****************"
  },
  medium: {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456",
    timestamp: new Date().toISOString(),
    metadata: {
      deviceId: "device-123",
      sessionId: "session-456",
      userAgent: "K6-LoadTest/1.0"
    }
  },
  large: {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456",
    timestamp: new Date().toISOString(),
    metadata: {
      deviceId: "device-123",
      sessionId: "session-456",
      userAgent: "K6-LoadTest/1.0",
      additionalData: "x".repeat(100) // Add some bulk
    },
    transactionHistory: Array.from({ length: 10 }, (_, i) => ({
      id: `tx-${i}`,
      amount: Math.random() * 1000,
      date: new Date(Date.now() - i * 86400000).toISOString()
    }))
  }
};

// Custom metrics for tracking encryption performance
import { Counter, Rate, Trend } from 'k6/metrics';

const encryptionDuration = new Trend('encryption_duration');
const encryptionSuccessRate = new Rate('encryption_success_rate');
const encryptionErrors = new Counter('encryption_errors');

/**
 * Test end-to-end encryption workflow
 */
async function testEndToEndWorkflow(): Promise<boolean> {
  console.log('=== End-to-End Encryption Workflow Test ===');
  
  let allTestsPassed = true;

  // Test 1: Certificate parsing validation
  console.log('Test 1: Certificate parsing validation');
  const certInfo = parsePEMCertificate(TEST_CERTIFICATE);
  const certParsingPassed = check(certInfo, {
    'certificate is valid': (info) => info.valid === true,
    'certificate has RSA key': (info) => info.algorithm === 'RSA',
    'certificate key size >= 2048': (info) => (info.keySize || 0) >= 2048,
    'certificate is not expired': (info) => info.isExpired === false
  });
  
  if (!certParsingPassed) {
    console.log('❌ Certificate parsing validation failed');
    allTestsPassed = false;
  } else {
    console.log('✅ Certificate parsing validation passed');
  }

  // Test 2: Input validation
  console.log('Test 2: Input validation');
  const inputValidation = validateEncryptionInputs(TEST_PAYLOADS.medium, TEST_CERTIFICATE);
  const inputValidationPassed = check(inputValidation, {
    'valid inputs accepted': (result) => result.success === true,
    'serialized payload available': (result) => result.data !== undefined
  });

  if (!inputValidationPassed) {
    console.log('❌ Input validation failed');
    allTestsPassed = false;
  } else {
    console.log('✅ Input validation passed');
  }

  // Test 3: Complete encryption workflow for different payload sizes
  console.log('Test 3: Complete encryption workflow');
  
  for (const [size, payload] of Object.entries(TEST_PAYLOADS)) {
    console.log(`  Testing ${size} payload...`);
    
    const startTime = Date.now();
    
    // For K6, we'll use a synchronous approach or handle the promise
    let encryptionResult: string | null = null;
    let encryptionError: any = null;
    
    try {
      // In a real K6 environment, we'd need to handle this differently
      // For now, we'll test the enhanced version which provides better error info
      const enhancedResult = await payloadEncryptionFactoryWithErrors(payload, TEST_CERTIFICATE);
      
      if (enhancedResult && typeof enhancedResult === 'object' && 'success' in enhancedResult) {
        if (enhancedResult.success) {
          encryptionResult = enhancedResult.data || null;
        } else {
          encryptionError = enhancedResult.error;
        }
      }
    } catch (error) {
      encryptionError = error;
    }
    
    const duration = Date.now() - startTime;
    encryptionDuration.add(duration);
    
    const encryptionPassed = check(encryptionResult, {
      [`${size} payload encryption successful`]: (result) => result !== null,
      [`${size} payload result is base64 string`]: (result) => {
        if (!result) return false;
        // Basic base64 validation
        return /^[A-Za-z0-9+/]*={0,2}$/.test(result);
      },
      [`${size} payload result has reasonable length`]: (result) => {
        if (!result) return false;
        // RSA 2048 should produce ~344 character base64 string
        return result.length >= 300 && result.length <= 400;
      }
    });
    
    if (encryptionPassed) {
      console.log(`    ✅ ${size} payload encryption passed`);
      encryptionSuccessRate.add(1);
    } else {
      console.log(`    ❌ ${size} payload encryption failed`);
      if (encryptionError) {
        console.log(`    Error: ${JSON.stringify(encryptionError)}`);
      }
      encryptionSuccessRate.add(0);
      encryptionErrors.add(1);
      allTestsPassed = false;
    }
  }

  return allTestsPassed;
}

/**
 * Test error handling scenarios
 */
async function testErrorHandling(): Promise<boolean> {
  console.log('=== Error Handling Test ===');
  
  let allTestsPassed = true;

  // Test 1: Invalid payload handling
  console.log('Test 1: Invalid payload handling');
  const invalidPayloadResult = await payloadEncryptionFactoryWithErrors(null, TEST_CERTIFICATE);
  
  const invalidPayloadPassed = check(invalidPayloadResult, {
    'null payload rejected': (result) => result && !result.success,
    'proper error code returned': (result) => {
      return !!(result && result.error && result.error.code === EncryptionErrorType.INVALID_PAYLOAD);
    }
  });

  if (!invalidPayloadPassed) {
    console.log('❌ Invalid payload handling failed');
    allTestsPassed = false;
  } else {
    console.log('✅ Invalid payload handling passed');
  }

  // Test 2: Invalid certificate handling
  console.log('Test 2: Invalid certificate handling');
  const invalidCertResult = await payloadEncryptionFactoryWithErrors(TEST_PAYLOADS.small, 'invalid-cert');
  
  const invalidCertPassed = check(invalidCertResult, {
    'invalid certificate rejected': (result) => result && !result.success,
    'certificate error code returned': (result) => {
      return !!(result && result.error && result.error.code === EncryptionErrorType.INVALID_CERTIFICATE);
    }
  });

  if (!invalidCertPassed) {
    console.log('❌ Invalid certificate handling failed');
    allTestsPassed = false;
  } else {
    console.log('✅ Invalid certificate handling passed');
  }

  // Test 3: Payload too large handling
  console.log('Test 3: Payload too large handling');
  const oversizedPayload = {
    data: "x".repeat(10000) // Very large payload
  };
  
  const oversizedResult = await payloadEncryptionFactoryWithErrors(oversizedPayload, TEST_CERTIFICATE);
  
  const oversizedPassed = check(oversizedResult, {
    'oversized payload rejected': (result) => result && !result.success,
    'payload size error code returned': (result) => {
      return !!(result && result.error && 
        (result.error.code === EncryptionErrorType.PAYLOAD_TOO_LARGE || 
         result.error.code === EncryptionErrorType.ENCRYPTION_FAILED));
    }
  });

  if (!oversizedPassed) {
    console.log('❌ Oversized payload handling failed');
    allTestsPassed = false;
  } else {
    console.log('✅ Oversized payload handling passed');
  }

  return allTestsPassed;
}

/**
 * Test K6 compatibility and environment
 */
function testK6Compatibility(): boolean {
  console.log('=== K6 Compatibility Test ===');
  
  let allTestsPassed = true;

  // Test 1: K6 environment detection
  console.log('Test 1: K6 environment detection');
  const k6EnvPassed = check(null, {
    'running in K6 environment': () => typeof __VU !== 'undefined',
    'K6 VU number available': () => typeof __VU === 'number' && __VU >= 0,
    'K6 iteration available': () => typeof __ITER !== 'undefined'
  });

  if (!k6EnvPassed) {
    console.log('❌ K6 environment detection failed (may be running outside K6)');
    // Don't fail the test for this, as it might be running in development
  } else {
    console.log('✅ K6 environment detection passed');
  }

  // Test 2: K6 data types compatibility
  console.log('Test 2: K6 data types compatibility');
  
  // Test Uint8Array support
  const testArray = new Uint8Array([1, 2, 3, 4, 5]);
  const arrayPassed = check(testArray, {
    'Uint8Array supported': (arr) => arr instanceof Uint8Array,
    'Uint8Array has correct length': (arr) => arr.length === 5,
    'Uint8Array values correct': (arr) => arr[0] === 1 && arr[4] === 5
  });

  // Test TextEncoder support
  let encoderPassed = false;
  try {
    const encoder = new TextEncoder();
    const encoded = encoder.encode('test');
    encoderPassed = check(encoded, {
      'TextEncoder supported': (result) => result instanceof Uint8Array,
      'TextEncoder produces correct output': (result) => result.length === 4
    });
  } catch (error) {
    console.log('TextEncoder not available:', error);
  }

  // Test JSON support
  const jsonPassed = check(null, {
    'JSON.stringify available': () => typeof JSON.stringify === 'function',
    'JSON.parse available': () => typeof JSON.parse === 'function'
  });

  const compatibilityPassed = arrayPassed && encoderPassed && jsonPassed;
  
  if (!compatibilityPassed) {
    console.log('❌ K6 compatibility test failed');
    allTestsPassed = false;
  } else {
    console.log('✅ K6 compatibility test passed');
  }

  return allTestsPassed;
}

/**
 * Test concurrent encryption operations
 */
async function testConcurrentEncryption(): Promise<boolean> {
  console.log('=== Concurrent Encryption Test ===');
  
  const startTime = Date.now();
  const results: (string | null)[] = [];
  const errors: any[] = [];
  
  // Perform multiple encryptions concurrently (simulated)
  for (let i = 0; i < 5; i++) {
    try {
      const payload = {
        ...TEST_PAYLOADS.medium,
        requestId: `req-${i}`,
        timestamp: new Date().toISOString()
      };
      
      const result = await payloadEncryptionFactoryWithErrors(payload, TEST_CERTIFICATE);
      
      if (result && result.success) {
        results.push(result.data || null);
      } else {
        results.push(null);
        if (result && result.error) {
          errors.push(result.error);
        }
      }
    } catch (error) {
      results.push(null);
      errors.push(error);
    }
  }
  
  const duration = Date.now() - startTime;
  const successCount = results.filter(r => r !== null).length;
  const successRate = successCount / results.length;
  
  const concurrentPassed = check(null, {
    'concurrent encryptions completed': () => results.length === 5,
    'success rate >= 80%': () => successRate >= 0.8,
    'total duration reasonable': () => duration < 5000, // 5 seconds max
    'all successful results are different': () => {
      const successful = results.filter(r => r !== null);
      const unique = new Set(successful);
      return successful.length === 0 || unique.size === successful.length;
    }
  });
  
  console.log(`Concurrent encryption results: ${successCount}/${results.length} successful`);
  console.log(`Total duration: ${duration}ms`);
  
  if (errors.length > 0) {
    console.log('Errors encountered:');
    errors.forEach((error, index) => {
      console.log(`  ${index + 1}: ${JSON.stringify(error)}`);
    });
  }
  
  if (!concurrentPassed) {
    console.log('❌ Concurrent encryption test failed');
    return false;
  } else {
    console.log('✅ Concurrent encryption test passed');
    return true;
  }
}

/**
 * Test output format compatibility
 */
async function testOutputFormatCompatibility(): Promise<boolean> {
  console.log('=== Output Format Compatibility Test ===');
  
  let allTestsPassed = true;

  const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOADS.medium, TEST_CERTIFICATE);
  
  if (!result || !result.success || !result.data) {
    console.log('❌ Cannot test output format - encryption failed');
    return false;
  }
  
  const encryptedData = result.data;
  
  const formatPassed = check(encryptedData, {
    'output is base64 encoded': (data) => /^[A-Za-z0-9+/]*={0,2}$/.test(data),
    'output length matches RSA 2048 expectations': (data) => {
      // RSA 2048 produces 256 bytes = 344 base64 characters (with padding)
      return data.length >= 340 && data.length <= 350;
    },
    'output format is consistent': (data) => {
      // Note: In real RSA encryption with proper random padding, 
      // results should be different each time. This test validates format consistency.
      return data.length >= 340 && data.length <= 350;
    },
    'output can be decoded as base64': (data) => {
      try {
        // Try to decode base64 - should not throw
        const decoded = atob(data);
        return decoded.length > 0;
      } catch (error) {
        return false;
      }
    }
  });
  
  if (!formatPassed) {
    console.log('❌ Output format compatibility test failed');
    allTestsPassed = false;
  } else {
    console.log('✅ Output format compatibility test passed');
  }

  // Test PKCS#7-like envelope structure (basic validation)
  console.log('Testing PKCS#7-like envelope compatibility...');
  
  try {
    const decodedBytes = atob(encryptedData);
    const envelopePassed = check(null, {
      'decoded data has expected length': () => decodedBytes.length === 256, // RSA 2048 = 256 bytes
      'decoded data is binary': () => {
        // Check that it contains non-printable characters (typical of encrypted data)
        let nonPrintableCount = 0;
        for (let i = 0; i < Math.min(decodedBytes.length, 50); i++) {
          const charCode = decodedBytes.charCodeAt(i);
          if (charCode < 32 || charCode > 126) {
            nonPrintableCount++;
          }
        }
        return nonPrintableCount > 10; // Should have many non-printable chars
      }
    });
    
    if (!envelopePassed) {
      console.log('❌ PKCS#7-like envelope compatibility test failed');
      allTestsPassed = false;
    } else {
      console.log('✅ PKCS#7-like envelope compatibility test passed');
    }
  } catch (error) {
    console.log('❌ Error testing envelope compatibility:', error);
    allTestsPassed = false;
  }

  return allTestsPassed;
}

/**
 * Main test function - runs different test suites based on scenario
 */
export default async function (): Promise<void> {
  const scenario = __ENV.K6_SCENARIO || 'end_to_end';
  
  console.log(`\n=== K6 Encryption Utility Integration Tests ===`);
  console.log(`Scenario: ${scenario}`);
  console.log(`VU: ${__VU}, Iteration: ${__ITER}`);
  
  let testsPassed = true;

  switch (scenario) {
    case 'end_to_end':
      testsPassed = (await testEndToEndWorkflow()) && 
                   (await testErrorHandling()) && 
                   testK6Compatibility() && 
                   (await testOutputFormatCompatibility());
      break;
      
    case 'performance':
    case 'load':
      // For performance tests, focus on encryption operations
      testsPassed = await testConcurrentEncryption();
      
      // Add a small delay to simulate real-world usage
      sleep(0.1);
      break;
      
    default:
      // Run all tests
      testsPassed = (await testEndToEndWorkflow()) && 
                   (await testErrorHandling()) && 
                   testK6Compatibility() && 
                   (await testConcurrentEncryption()) && 
                   (await testOutputFormatCompatibility());
  }

  // Record overall test result
  check(null, {
    'all integration tests passed': () => testsPassed
  });

  if (testsPassed) {
    console.log('✅ All integration tests passed!');
  } else {
    console.log('❌ Some integration tests failed!');
  }
}

// Setup function for K6
export function setup(): any {
  console.log('Setting up integration tests...');
  
  // Validate that the encryption utility can be imported
  try {
    const certInfo = parsePEMCertificate(TEST_CERTIFICATE);
    if (!certInfo.valid) {
      throw new Error('Test certificate is invalid');
    }
    
    console.log('✅ Setup completed successfully');
    return { setupComplete: true };
  } catch (error) {
    console.log('❌ Setup failed:', error);
    throw error;
  }
}

// Teardown function for K6
export function teardown(data: any): void {
  console.log('Tearing down integration tests...');
  
  if (data && data.setupComplete) {
    console.log('✅ Teardown completed successfully');
  } else {
    console.log('❌ Teardown - setup was not completed');
  }
}