import { setupVu } from "./core/setup";
import {cardManagement,  cardManagementExec} from "./scenarios";

// Initialize the test environment
setupVu();

// Configure global options for the load test
export const options = {
    batch: 10,
    batchPerHost: 5,
    noVUConnectionReuse: true,
    scenarios: {
        UserCardManagement: cardManagement("smoke"),
    },
};



// Export the execution function for direct use
export { cardManagementExec };