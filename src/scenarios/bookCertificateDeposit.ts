import { fail, sleep } from "k6";
import { getFxRate, listAccountsForBookDeposits } from "../actions/accounts";
import {
  bookCertificateDeposits,
  getDepositCatalogCurrency,
  getDepositCatalogTenor,
  getDepositCatalogType,
  getDepositList,
  getDepositsTermsConditions,
  getDepositUserEligibility,
  postDepositInterestRate,
} from "../actions/deposits";

import { login, logout } from "../actions/login";

import exec from "k6/execution";
import { getEligibleBookDepositAccount } from "../actions/accounts/accounts-utils";
import { callHomepage } from "../actions/homepage";
import { getTargetVus } from "../core/helpers/getTargetVus";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 200;

export function bookCertificateDepositExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.bookDepositsSubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: bookDepositsSubUsers`,
      );
    }


    const deviceId = user.data.deviceId
    const token = login(user, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      getFxRate(customHeaders);
      getDepositList(customHeaders);
      getDepositCatalogType(customHeaders);
      getDepositCatalogCurrency(customHeaders);
      getDepositCatalogTenor(customHeaders);
      getDepositUserEligibility(customHeaders);
      getDepositsTermsConditions(customHeaders);
      const listOfTransferAccounts: any = listAccountsForBookDeposits(customHeaders);
      const eligibleLocalTransferAccount = getEligibleBookDepositAccount(
        listOfTransferAccounts,
      );
      if (!eligibleLocalTransferAccount) {
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible account",
        );
      }
      const bookBody = {
        type: "CD",
        frequency: "Fixed - Monthly",
        currency: "EGP",
        tenor: "3 years 9.5% - Minimum 1K",
        amount: 1000,
        maturity: "Close on maturity",
        debitAccountId: eligibleLocalTransferAccount.id,
        principalCreditAccountId: eligibleLocalTransferAccount.id,
        interestCreditAccountId: eligibleLocalTransferAccount.id,
        code: "EGP.1000.01M.36M",
      };
      const interestBody = {
        type: "CD",
        frequency: "Fixed - Monthly",
        currency: "EGP",
        tenor: "3 years 9.5% - Minimum 1K",
        amount: 1000,
        maturity: "Close on maturity",
        debitAccountId: eligibleLocalTransferAccount.id,
        principalCreditAccountId: eligibleLocalTransferAccount.id,
        interestCreditAccountId: eligibleLocalTransferAccount.id,
        code: "EGP.1000.01M.36M",
      };
      postDepositInterestRate(interestBody, customHeaders);
      bookCertificateDeposits(bookBody, customHeaders);
      sleep(10)
      listAccountsForBookDeposits(customHeaders)
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function bookCertificateDeposit(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "bookCertificateDeposit");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: bookCertificateDepositExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: bookCertificateDepositExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 14,
        iterations: 1,
        exec: bookCertificateDepositExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: bookCertificateDepositExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
