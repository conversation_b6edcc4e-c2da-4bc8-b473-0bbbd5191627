import { fail, sleep } from "k6";
import { listStocks } from "../actions/custoday/listStocks";
import { login, logout } from "../actions/login";
import exec from "k6/execution";
import { callHomepage } from "../actions/homepage";
import {getNextUser} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";
import {getUserDetails} from "../actions/users/getUserDetails";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 180;

export function StocksDetailsExc()
{
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.custodySubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password)
      {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: depositsListSubUsers`,
      );
    }

    const deviceId = `${user.username}_deviceId5`
    const token = login(user, deviceId);
    sleep(waitTimeAfterLogin);
    if (token)
      {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      const userDetails: any = getUserDetails(customHeaders);
      if (!userDetails || !userDetails.data.haveSecuritiesPortfolio)
    {
    return;
  }
  const securitiesListDat: any = listStocks(customHeaders);

  if (!securitiesListDat || !securitiesListDat.data || securitiesListDat.data.length === 0)
    {
    return;
  }
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function Stockdetails(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "stockdetails");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: StocksDetailsExc.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: StocksDetailsExc.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: StocksDetailsExc.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: StocksDetailsExc.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
