import { listAccountsForLocalTransfer, getFxRate } from "../actions/accounts";
import {
  createOutsideCibTransfer,
  getBeneficiaries,
  getBeneficiariesCategories,
} from "../actions/createTransfer";
import { login, logout } from "../actions/login";

import exec from "k6/execution";
import { fail, sleep } from "k6";
import { getEligibleLocalTransferAccount } from "../actions/accounts/accounts-utils";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 123;

export function createTransferOutsideCIBExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.transfersOutsideCibSubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: transfersOutsideCibSubUsers`,
      );
    }

    const deviceId = user.data.deviceId
    const token = login(user, deviceId);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      sleep(waitTimeAfterLogin);
      const listOfTransferAccounts: any = listAccountsForLocalTransfer(customHeaders);
      const eligibleLocalTransferAccount = getEligibleLocalTransferAccount(
        listOfTransferAccounts,
      );
      if (!eligibleLocalTransferAccount) {
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible account",
        );
      }
      const transferBody = {
        description: "test desc",
        name: "Test Test",
        feesChargesOn: "shared",
        sender: {
          accountNumber: eligibleLocalTransferAccount.accountNumber,
          currency: eligibleLocalTransferAccount.currency,
        },
        recipient: {
          name: "Test Test",
          accountNumber: "*************",
          currency: "EGP",
          address: "Cairo",
          type: "domestic",
          country: "EG",
          bank: {
            swiftCode: "EBBKEGCXXXX",
            country: "EG",
            name: "HSBC BANK EGYPT",
          },
          amount: {
            amount: 10,
            currency: "EGP",
          },
        },
        beneficiaryType: "new",
        otp: "123455",
        disclaimer: true,
      };
      getBeneficiaries(customHeaders);
      getBeneficiariesCategories(customHeaders);
      getFxRate(customHeaders);
      createOutsideCibTransfer(transferBody, customHeaders);
      sleep(10)
      listAccountsForLocalTransfer(customHeaders)
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function createOutSideCibTransfer(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "createOutSideCibTransfer");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: createTransferOutsideCIBExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: createTransferOutsideCIBExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: createTransferOutsideCIBExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: createTransferOutsideCIBExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
