import { login, logout } from "../actions/login";
import exec from "k6/execution";

import { listCreditCards } from "../actions/creditCards";
import { fail, sleep } from "k6";
import { getEligibleLocalTransferAccount } from "../actions/accounts/accounts-utils";
import { createCreditCardTranfer } from "../actions/createTransfer";

import { listAccountsForCardsTransfer, getFxRate } from "../actions/accounts";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 139;

export function creditCardTransferExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.ccTransferSubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: ccTransferSubUsers`,
      );
    }

    const deviceId = user.data.deviceId
    const token = login(user, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      sleep(waitTimeAfterLogin);
      var cardList: any = listCreditCards(customHeaders);
      if (cardList.length === 0) {
        throw new Error("Use Case Failure - The user doesnt hold a valid card");
      }
      var cardNumber = cardList[0].accountNumber;
      // getCreditCardDetails(cardNumber, customHeaders);
      // getCreditCardTransactions(cardNumber, customHeaders);
      const listOfTransferAccounts: any = listAccountsForCardsTransfer(customHeaders);
      const eligibleLocalTransferAccount = getEligibleLocalTransferAccount(
        listOfTransferAccounts,
      );
      if (!eligibleLocalTransferAccount) {
        throw new Error(
          "Use Case Failure - User doesnt hold an aligilbe account",
        );
      }
      const firstAccountNumber = eligibleLocalTransferAccount.accountNumber;
      const currency = eligibleLocalTransferAccount.currency;
      const transferBody = {
        sender: {
          accountNumber: firstAccountNumber,
          currency: currency,
        },
        amount: {
          amount: 10,
          currency: "EGP",
        },
        disclaimer: true,
        recipient: {
          accountNumber: cardNumber,
          currency: "EGP",
          type: "card",
        },
      };
      getFxRate(customHeaders);
      createCreditCardTranfer(transferBody, customHeaders);
      sleep(10)
      listAccountsForCardsTransfer(customHeaders)
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function creditCardTransfer(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "creditCardTransfer");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: creditCardTransferExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: creditCardTransferExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: creditCardTransferExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: creditCardTransferExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
