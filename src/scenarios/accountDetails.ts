import { login, logout } from "../actions/login";
import exec from "k6/execution";
import {
  listAccounts,
  getAccountTransactions,
  getAccountDetails,
} from "../actions/accounts";
import { sleep } from "k6";
import { fail } from "k6";
import { callHomepage } from "../actions/homepage";
import { getTargetVus } from "../core/helpers/getTargetVus";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import {User} from "../typings/globals";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 52;

export  function accountTransactionExec() {
  const user: User = getNextUser(exec.vu.idInTest, globalThis.accountsSubUsers);
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: accountsSubUsers`,
      );
    }

    const deviceId = user.data.deviceId
    const token = login(user, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      const accountsListData: any = listAccounts(customHeaders);
      if (
        !accountsListData?.accounts ||
        accountsListData.accounts.length === 0
      ) {
        throw new Error(
          "Use Case Failure - The user doesnt hold an eligible account",
        );
      }
      const accountNumber = accountsListData.accounts[0].accountNumber;
      const accountId = accountsListData.accounts[0].id;
      getAccountDetails(accountNumber, customHeaders);
      getAccountTransactions(accountId, customHeaders);
      sleep(10)
      listAccounts(customHeaders)

    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {
    if (
      error.message.includes("Authentication Failure") ||
      error.message.includes("Use Case Failure")
    ) {
      console.log(
        `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
      );
    }
    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function accountTransaction(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "accountTransaction");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: accountTransactionExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "300s",
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: accountTransactionExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: accountTransactionExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: accountTransactionExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
