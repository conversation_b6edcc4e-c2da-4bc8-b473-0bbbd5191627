import {fail, sleep} from 'k6';
import {getNextUser} from "../core/helpers/getExcludedArray";
import exec from "k6/execution";
import {login, logout} from "../actions/login";
import {callHomepage} from "../actions/homepage";
import {
    getGetEmbossingList,
    getGetMeta, postIseligibile,
    postIssuanceSubmit,
    postIssuanceSummary
} from "../actions/debitCards/DebitCardIssuance";
import {getTargetVus} from "../core/helpers/getTargetVus";
import {cardManagementExec} from "./cardManagement";
import {getAccountDetails, listAccounts} from "../actions/accounts";
const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 182;
export function debitCardIssuanceExec() {
    const user: any = getNextUser(
        exec.vu.idInTest,
        globalThis.debitCardIssuanceUsers,
    );
    let customHeaders: any = null;

    // Navigate to homepage
    try {
        if (!user.username || !user.password) {
            console.log(`Warning: User with id ${exec.vu.idInTest} has invalid username or password. Data file: ipnTransferSubUsers`);
            // Skip this iteration but don't fail the test
            return;
        }
        // Login and get session token
        const deviceId = user.data.deviceId
        const token = login(user, deviceId);
        sleep(waitTimeAfterLogin);
        if (token) {
            customHeaders = { token, deviceId };
            callHomepage(customHeaders);

    // Primary flow
            const accountsListData: any = listAccounts(customHeaders);
            if (
                !accountsListData?.accounts ||
                accountsListData.accounts.length === 0
            ) {
                throw new Error(
                    "Use Case Failure - The user doesnt hold an eligible account",
                );
            }
            for (let i = 0; i < accountsListData.accounts.length; i++) {
                var account = {}
                const singleAccount = accountsListData.accounts[i]
                const isEligible = postIseligibile({
                    "accountSignature": singleAccount.id,
                    "nameToBeEmbossedIndex": 0
                }, customHeaders)
                if (isEligible) {
                    const accountDetails:any =  getAccountDetails(singleAccount.accountNumber, customHeaders);
                    const issuanceOperation: any = accountDetails?.operations?.issueDebitCard.enabled
                        if (issuanceOperation) {
                        getGetEmbossingList(customHeaders);
                        sleep(1);
                        getGetMeta(customHeaders);
                        sleep(1)
                        postIssuanceSummary({
                            "accountSignature": singleAccount.id,
                            "nameToBeEmbossedIndex": 0
                        }, customHeaders);
                        sleep(1)
                        postIssuanceSubmit({
                            "accountSignature": singleAccount.id,
                            "nameToBeEmbossedIndex": 0,
                            "mailingAddress": {
                                "governorate": "1",
                                "area": "1",
                                "streetName": "123",
                                "buildingNumber": "sdsd",
                                "floorNumber": "sdsd",
                                "appartmentNumber": "sdsd"
                            },
                            "otp": "331477"
                        }, customHeaders);

                    }
                        break
                }

            }
    // Logout to clean up session
    logout(customHeaders);
        } } catch (error: any) {
        if (
            error.message.includes("Authentication Failure") ||
            error.message.includes("Use Case Failure")
        ) {
            console.log(
                `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
            );
        }
        sleep(waitTimeAfterVu * 3);
        fail(`Unexpected error: ${error?.message}`);
    } finally {
        if (customHeaders) {
            logout(customHeaders);
        }
        sleep(waitTimeAfterVu);
    }
}
export function debitCardIssuance(
    type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
    const targetVUs = getTargetVus(type, "debitCardIssuance");

    switch (type) {
        case "load":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "15m", target: targetVUs },
                    { duration: "50m", target: targetVUs },
                ],
                exec: cardManagementExec.name,
            };
        case "endurance":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                stages: [
                    { duration: "1h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "12h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "1h", target: 0 },
                ],
                exec: cardManagementExec.name,
            };
        case "smoke":
            return {
                executor: "per-vu-iterations",
                vus: 2,
                iterations: 1,
                exec: cardManagementExec.name,
            };
        case "shock":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                ],
                exec: cardManagementExec.name,
            };
        default:
            throw new Error("Invalid type");
    }
}