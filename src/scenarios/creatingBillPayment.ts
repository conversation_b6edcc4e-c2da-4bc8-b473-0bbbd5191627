import { login, logout } from "../actions/login";
import exec from "k6/execution";

import { fail, sleep } from "k6";
import { getEligibleForBillPayment } from "../actions/accounts/accounts-utils";

import { listAccountsForBillPayment } from "../actions/accounts";
import {
  getBillPaymentGroups,
  getFavoritesBills,
  getBillers,
} from "../actions/bills/billpaymentInquiries";
import {
  billPaymentLookup,
  billPaymentConfirmation,
  billPay,
} from "../actions/bills/payBills";
import { callHomepage } from "../actions/homepage";
import {
  //addUserToExcluded,
  getNextUser,
} from "../core/helpers/getExcludedArray";
import { getTargetVus } from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 30;
const waitTimeAfterVu = 185;

export function billPaymentExec() {
  const user: any = getNextUser(
    exec.vu.idInTest,
    globalThis.billPaymentSubUsers,
  );
  let customHeaders: any = null;
  try {
    if (!user.username || !user.password) {
      throw new Error(
        `User with id ${exec.vu.idInTest} has invalid username or password. Data file: billPaymentSubUsers`,
      );
    }
    const randomString = Math.random().toString(36).slice(2, 7)

    const deviceId = user.data.deviceId
    const token = login(user, deviceId);
    sleep(waitTimeAfterLogin);
    if (token) {
      customHeaders = {token, deviceId}
      callHomepage(customHeaders);
      getBillPaymentGroups(customHeaders);
      getFavoritesBills(customHeaders);
      getBillers(customHeaders);

      const listOfTransferAccounts: any = listAccountsForBillPayment(customHeaders);
      const eligibleLocalTransferAccount = getEligibleForBillPayment(
        listOfTransferAccounts,
      );
      if (!eligibleLocalTransferAccount) {
        throw new Error(
          "Use Case Failure - The user doesnot have eligible account",
        );
      }
      const firstAccountNumber = eligibleLocalTransferAccount.accountNumber;
      const firstAccountID = eligibleLocalTransferAccount.id;
      const sourcePaymentId = eligibleLocalTransferAccount.value;
      const currency = eligibleLocalTransferAccount.currency;
      const date = new Date();
      const createdDate = date.toLocaleDateString();
      const lookupBody = {
        billingAccount: "***********",
        billerId: "1",
        serviceId: "121",
      };
      const sepecificID = billPaymentLookup(lookupBody, customHeaders);
      const confirmationBody = {
        billerId: "1",
        billerName: "Vodafone",
        amount: 500,
        sourcePaymentCurrency: "EGP",
        sourcePaymentId: sourcePaymentId,
        paymentMethod: "account",
        accountNumber: firstAccountID,
        serviceId: "121",
        serviceName: "Vodafone Topup",
        paymentType: "prepaid",
        billingAccount: "***********",
        groupName: "Recharge Mobiles",
        date: createdDate,
        billSpecific: sepecificID,
      };

      var requestID = billPaymentConfirmation(confirmationBody, customHeaders);
      const payBody = {
        otp: "111111",
        requestId: requestID,
      };
      billPay(payBody, customHeaders);
      sleep(10)
      listAccountsForBillPayment(customHeaders)
    } else {
      throw new Error("Authentication Failure - The user was not logged in");
    }
  } catch (error: any) {

    //addUserToExcluded(user);
    sleep(waitTimeAfterVu * 3);
    fail(`Unexpected error: ${error?.message}`);
  } finally {
    if (customHeaders) {
      logout(customHeaders);
    }
    sleep(waitTimeAfterVu);
  }
}

export function billPayment(
  type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
  const targetVUs = getTargetVus(type, "billPayment");
  switch (type) {
    case "load":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "15m", target: targetVUs },
          { duration: "50m", target: targetVUs },
        ],
        exec: billPaymentExec.name,
      };
    case "endurance":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        stages: [
          { duration: "1h", target: targetVUs },
          { duration: "12h", target: targetVUs },
          { duration: "1h", target: 0 },
        ],
        exec: billPaymentExec.name,
      };
    case "smoke":
      return {
        executor: "per-vu-iterations",
        vus: 1,
        iterations: 1,
        exec: billPaymentExec.name,
      };
    case "shock":
      return {
        executor: "ramping-vus",
        startVUs: 0,
        gracefulRampDown: "600s",
        stages: [
          { duration: "5m", target: targetVUs },
          { duration: "5m", target: targetVUs },
        ],
        exec: billPaymentExec.name,
      };
    default:
      throw new Error("Invalid type");
  }
}
