import {fail, JSONValue, sleep} from 'k6';
import {callHomepage} from "../actions/homepage";
import {
    getDepositDetails,
    getDepositList, redeemSubmit, redeemSummary, validateIfDepositBreakable,
    // redeemSubmit,
    // redeemSummary,
    // validateIfDepositBreakable
} from "../actions/deposits";
import {login, logout} from "../actions/login";
import {getNextUser} from "../core/helpers/getExcludedArray";
import exec from "k6/execution";
import {getTargetVus} from "../core/helpers/getTargetVus";

const waitTimeAfterLogin = 1;
const waitTimeAfterVu = 1;

export function depositRedemptionExec() {
    // Login and get session token
    const user: any = getNextUser(
        exec.vu.idInTest,
        globalThis.depositRedemptionUsers,
    );
    let customHeaders: any = null;

    try {
        if (!user.username || !user.password) {
            console.log(`Warning: User with id ${exec.vu.idInTest} has invalid username or password. Data file: Deposit Redemption`);
            // Skip this iteration but don't fail the test
            return;
        }

        const deviceId = user.data.deviceId
        const token = login(user, deviceId);
        customHeaders = { token, deviceId };

        sleep(waitTimeAfterLogin);

        if (token) {
            customHeaders = {token, deviceId};

            // Call homepage to simulate user navigation
            callHomepage(customHeaders);
            // Primary flow
            const depositList : any = getDepositList(customHeaders);
            sleep(1);
            if (depositList.length > 0) {
                getDepositDetails(depositList[1].id, customHeaders);
                sleep(1);
              const validateBreakable: any = validateIfDepositBreakable(depositList[1].id, customHeaders);
                sleep(1);
                if (validateBreakable.isEligibleForBreak){
                    redeemSummary(depositList[1].id, {
                        "breakType": "partial",
                        "amount": depositList[1].amount
                    }, customHeaders);
                    sleep(1);
                    redeemSubmit(depositList[1].id, {
                        "breakType": "partial",
                        "amount": depositList[1].amount
                    }, customHeaders);
                }

                // Logout to clean up session
                logout(customHeaders);
            }

        }
    }catch (error: any) {
            if (
                error.message.includes("Authentication Failure") ||
                error.message.includes("Use Case Failure")
            ) {
                console.log(
                    `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
                );
            }
            sleep(waitTimeAfterVu * 3);
            fail(`Unexpected error: ${error?.message}`);
        } finally {
            if (customHeaders) {
                logout(customHeaders);
            }
            sleep(waitTimeAfterVu);
        }
    }

export function depositRedemption(
    type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
    const targetVUs = getTargetVus(type, "depositRedemption");

    switch (type) {
        case "load":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "15m", target: targetVUs },
                    { duration: "50m", target: targetVUs },
                ],
                exec: depositRedemptionExec.name,
            };
        case "endurance":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                stages: [
                    { duration: "1h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "12h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "1h", target: 0 },
                ],
                exec: depositRedemptionExec.name,
            };
        case "smoke":
            return {
                executor: "per-vu-iterations",
                vus: 1,
                iterations: 1,
                exec: depositRedemptionExec.name,
            };
        case "shock":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                ],
                exec: depositRedemptionExec.name,
            };
        default:
            throw new Error("Invalid type");
    }
}

