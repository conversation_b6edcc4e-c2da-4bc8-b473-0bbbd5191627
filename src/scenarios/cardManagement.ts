import {fail, sleep} from 'k6';
import {login, logout, payloadEncryptionKey} from "../actions/login";
import {getNextUser} from "../core/helpers/getExcludedArray";
import exec from "k6/execution";
import {callHomepage} from "../actions/homepage";
import {
    postActiveCards,

    postFreezeCard, postResetPinOtp, postSendOtp,
    postUnfreezeCard
} from "../actions/cardManagement/CardManagement";
import {getTargetVus} from "../core/helpers/getTargetVus";
import {debitCardDetails, listDebitCards} from "../actions/debitCards/listDeditCards";


const waitTimeAfterLogin = 1;
const waitTimeAfterVu = 1;
export async function cardManagementExec() {
    const user: any = getNextUser(
        exec.vu.idInTest,
        globalThis.cardManagementUsers,
    );
    let customHeaders: any = null;

    try {
        if (!user.username || !user.password) {
            console.log(`Warning: User with id ${exec.vu.idInTest} has invalid username or password. Data file: ipnTransferSubUsers`);
            // Skip this iteration but don't fail the test
            return;
        }
        // Login and get session token
        const deviceId = user.data.deviceId
        const token = login(user, deviceId);
        sleep(waitTimeAfterLogin);
        if (token) {
            customHeaders = { token, deviceId };
            // callHomepage(customHeaders);
         const debitCards : any =  listDebitCards(customHeaders)
            if (debitCards.length > 0) {
                var activatedCard = [];
                var unActiveCard = []
                for (let i = 0; i < debitCards.length; i++) {
                    if (debitCards[i].isFrozen === false && debitCards[i].isActive) {
                        activatedCard.push(debitCards[i]);
                    }else if ( !debitCards[i].isActive) {
                        unActiveCard.push(debitCards[i]);
                    }
                }
                if (activatedCard.length > 0) {
                    const cardDetails: any = debitCardDetails(activatedCard[0].id, customHeaders );
                    // postFreezeCard({
                    //     "cardType": "DebitCard",
                    //     "otp": ""
                    // }, cardDetails.cardInfo.id, customHeaders);

                    // sleep(1);
                    // postSendOtp({
                    //     "cardType": "DebitCard",
                    //     "cardId": cardDetails.cardInfo.id,
                    // }, customHeaders);
                    // sleep(1);

                    // postUnfreezeCard({
                    //     "cardType": "DebitCard",
                    //     "otp": "123456"
                    // }, cardDetails.cardInfo.id, customHeaders);

                    // sleep(1);

                    postSendOtp({
                        "cardType": "DebitCard",
                        "cardId": cardDetails.cardInfo.id,
                    }, customHeaders);


                    // postSendOtp({
                    //     "cardType": "CreditCard",
                    //     "cardId": cardDetails.accountNumber
                    // }, customHeaders);

                    // postValidatePin({
                    //     "pin": "123456",
                    //     "cardId": cardDetails.accountNumber,
                    //     "cardType": "CreditCard"
                    // }, customHeaders);
        const payloadEncryption : string  = `${payloadEncryptionKey(customHeaders)}`
        // const payloadEncryption : string  = `${payloadEncryptionKey(customHeaders)}`
                    console.log("payloadEncryption :-" , payloadEncryption)
                    sleep(1);
                   await postResetPinOtp({
                        "pin": "1234",
                        "cardId": activatedCard[0].id,
                        "cardType": "DebitCard",
                        "otp": "123456"
                    },payloadEncryption, customHeaders);
                    sleep(1);
                }
                if (unActiveCard.length > 0) {
                    const UnActiveCardDetails: any = debitCardDetails(unActiveCard[0].id, customHeaders );

                    postActiveCards({
                        "cardId": UnActiveCardDetails.cardInfo.id,
                        "cardType": "DebitCard",
                        "expiryDate": "01/30",
                        "otp": "123456"
                    }, customHeaders);
                }
            }
            // Primary flow

            // postStop({
            //     "returnTermsConditions": false,
            //     "cardId": debitCards[0].cardId,
            //     "maskedCardNumber": debitCards[0].maskedCardNumber,
            //     "accountNumber": debitCards[0].accountNumber,
            //     "reason": "Lost"
            // },debitCards[0].cardId, customHeaders);
            // sleep(1);


            // postResetPinAuthkey({
            //     "cardId": debitCards[0].accountNumber,
            //     "cardType": "CreditCard",
            //     "pin": "1234",
            //     "authKey": "a0039291-2ef8-4fa1-b23b-d28836a416ec"
            // }, customHeaders);
            // sleep(1);
            // postResetPinOtp({
            //     "pin": "1234",
            //     "cardId": debitCards[0].accountNumber,
            //     "cardType": "CreditCard",
            //     "otp": "1234"
            // }, customHeaders);
            // sleep(1);

            //
            // sleep(1);
            // postValidateOtp({
            //     "cardType": "DebitCard",
            //     "cardId": debitCards[0].accountNumber,
            //     "otp": "1234"
            // }, customHeaders);


        } } catch (error: any) {
            if (
                error.message.includes("Authentication Failure") ||
                error.message.includes("Use Case Failure")
            ) {
                console.log(
                    `Bad test data: ${exec.scenario.name}\t ${user.username} - ${error?.message} - ${error?.status}`,
                );
            }
            sleep(waitTimeAfterVu * 3);
            fail(`Unexpected error: ${error?.message}`);
        } finally {
            if (customHeaders) {
                logout(customHeaders);
            }
            sleep(waitTimeAfterVu);
        }



}
/**
* Configures the load test scenario based on the specified type
* @param type The type of load test to configure
* @returns The load test configuration
*/
export function cardManagement(
    type: "load" | "stress" | "endurance" | "smoke" | "shock",
) {
    const targetVUs = getTargetVus(type, "cardManagement");

    switch (type) {
        case "load":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "15m", target: targetVUs },
                    { duration: "50m", target: targetVUs },
                ],
                exec: cardManagementExec.name,
            };
        case "endurance":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                stages: [
                    { duration: "1h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "12h", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "1h", target: 0 },
                ],
                exec: cardManagementExec.name,
            };
        case "smoke":
            return {
                executor: "per-vu-iterations",
                vus: 1,
                iterations: 1,
                exec: cardManagementExec.name,
            };
        case "shock":
            return {
                executor: "ramping-vus",
                startVUs: 1,
                gracefulRampDown: "600s",
                stages: [
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                    { duration: "5m", target: targetVUs > 0 ? targetVUs : 5 },
                ],
                exec: cardManagementExec.name,
            };
        default:
            throw new Error("Invalid type");
    }
}