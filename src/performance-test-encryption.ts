/**
 * Performance tests for K6 encryption utility
 * Focuses on concurrent encryption operations and performance metrics
 */

import { check, sleep } from 'k6';
import { Options } from 'k6/options';
import { Counter, Rate, Trend, Gauge } from 'k6/metrics';
import {
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate
} from './utils/encryption';

// Performance test configuration
export const options: Options = {
  scenarios: {
    // Stress test with high concurrency
    stress_test: {
      executor: 'constant-vus',
      vus: 50,
      duration: '60s',
      tags: { test_type: 'stress' }
    },
    // Spike test
    spike_test: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: '10s', target: 1 },
        { duration: '5s', target: 100 }, // Spike
        { duration: '10s', target: 100 },
        { duration: '5s', target: 1 }, // Drop
        { duration: '10s', target: 1 }
      ],
      tags: { test_type: 'spike' }
    },
    // Endurance test
    endurance_test: {
      executor: 'constant-vus',
      vus: 10,
      duration: '300s', // 5 minutes
      tags: { test_type: 'endurance' }
    }
  },
  thresholds: {
    'encryption_duration': ['p(95)<500', 'p(99)<1000'], // 95% under 500ms, 99% under 1s
    'encryption_success_rate': ['rate>0.99'], // 99% success rate
    'encryption_throughput': ['rate>10'], // At least 10 encryptions per second
    'memory_usage': ['value<100'], // Memory usage indicator
    'checks': ['rate>0.95']
  }
};

// Test certificate
const TEST_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==
-----END CERTIFICATE-----`;

// Custom metrics for detailed performance tracking
const encryptionDuration = new Trend('encryption_duration');
const encryptionSuccessRate = new Rate('encryption_success_rate');
const encryptionThroughput = new Rate('encryption_throughput');
const encryptionErrors = new Counter('encryption_errors');
const memoryUsage = new Gauge('memory_usage');
const concurrentOperations = new Gauge('concurrent_operations');

// Payload templates for different test scenarios
const PAYLOAD_TEMPLATES = {
  minimal: {
    id: "{{ID}}",
    timestamp: "{{TIMESTAMP}}"
  },
  standard: {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456",
    requestId: "{{ID}}",
    timestamp: "{{TIMESTAMP}}"
  },
  complex: {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456",
    requestId: "{{ID}}",
    timestamp: "{{TIMESTAMP}}",
    metadata: {
      deviceId: "device-{{ID}}",
      sessionId: "session-{{ID}}",
      userAgent: "K6-LoadTest/1.0",
      ipAddress: "192.168.1.{{ID}}",
      browserFingerprint: "fp-{{ID}}"
    },
    transactionData: {
      amount: "{{AMOUNT}}",
      currency: "USD",
      merchantId: "merchant-{{ID}}",
      description: "Test transaction {{ID}}"
    }
  }
};

/**
 * Generate a test payload from template
 */
function generatePayload(template: any, id: number): any {
  const payload = JSON.parse(JSON.stringify(template));
  
  function replaceTokens(obj: any): any {
    if (typeof obj === 'string') {
      return obj
        .replace(/\{\{ID\}\}/g, String(id))
        .replace(/\{\{TIMESTAMP\}\}/g, new Date().toISOString())
        .replace(/\{\{AMOUNT\}\}/g, String((Math.random() * 1000).toFixed(2)));
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        obj[key] = replaceTokens(obj[key]);
      }
    }
    return obj;
  }
  
  return replaceTokens(payload);
}

/**
 * Perform a single encryption operation with metrics
 */
async function performEncryption(payloadTemplate: any, operationId: number): Promise<boolean> {
  const startTime = Date.now();
  
  try {
    // Generate unique payload
    const payload = generatePayload(payloadTemplate, operationId);
    
    // Track concurrent operations
    concurrentOperations.add(1);
    
    // Perform encryption
    const result = await payloadEncryptionFactoryWithErrors(payload, TEST_CERTIFICATE);
    
    const duration = Date.now() - startTime;
    encryptionDuration.add(duration);
    
    // Validate result
    const success = result && result.success && result.data !== null;
    
    if (success) {
      encryptionSuccessRate.add(1);
      encryptionThroughput.add(1);
      
      // Basic validation of encrypted result
      const isValidBase64 = /^[A-Za-z0-9+/]*={0,2}$/.test(result.data!);
      const hasCorrectLength = result.data!.length >= 340 && result.data!.length <= 350;
      
      if (!isValidBase64 || !hasCorrectLength) {
        console.log(`Warning: Encrypted result validation failed for operation ${operationId}`);
        return false;
      }
      
      return true;
    } else {
      encryptionSuccessRate.add(0);
      encryptionErrors.add(1);
      
      if (result && result.error) {
        console.log(`Encryption failed for operation ${operationId}: ${result.error.message}`);
      }
      
      return false;
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    encryptionDuration.add(duration);
    encryptionSuccessRate.add(0);
    encryptionErrors.add(1);
    
    console.log(`Encryption error for operation ${operationId}: ${error}`);
    return false;
  } finally {
    concurrentOperations.add(-1);
  }
}

/**
 * Memory usage estimation (simplified)
 */
function estimateMemoryUsage(): number {
  // Simple heuristic based on operation count and time
  // In a real scenario, this would use actual memory monitoring
  // Simplified memory usage estimation
  const avgDuration = 100; // Estimated average duration in ms
  
  // Estimate memory usage based on duration
  return Math.max(1, avgDuration / 100);
}

/**
 * Stress test scenario
 */
async function stressTest(): Promise<void> {
  const operationId = __VU * 10000 + __ITER;
  
  // Use different payload complexities based on VU
  let template;
  if (__VU % 3 === 0) {
    template = PAYLOAD_TEMPLATES.minimal;
  } else if (__VU % 3 === 1) {
    template = PAYLOAD_TEMPLATES.standard;
  } else {
    template = PAYLOAD_TEMPLATES.complex;
  }
  
  const success = await performEncryption(template, operationId);
  
  check(null, {
    'stress test encryption successful': () => success
  });
  
  // Update memory usage estimate
  memoryUsage.add(estimateMemoryUsage());
  
  // Small random delay to simulate real-world variance
  sleep(Math.random() * 0.1);
}

/**
 * Spike test scenario
 */
async function spikeTest(): Promise<void> {
  const operationId = __VU * 10000 + __ITER;
  
  // Use standard payload for spike test
  const success = await performEncryption(PAYLOAD_TEMPLATES.standard, operationId);
  
  check(null, {
    'spike test encryption successful': () => success
  });
  
  // No delay during spike test to maximize load
}

/**
 * Endurance test scenario
 */
async function enduranceTest(): Promise<void> {
  const operationId = __VU * 10000 + __ITER;
  
  // Rotate through different payload types for endurance
  const templateIndex = __ITER % 3;
  const templates = [PAYLOAD_TEMPLATES.minimal, PAYLOAD_TEMPLATES.standard, PAYLOAD_TEMPLATES.complex];
  const template = templates[templateIndex];
  
  const success = await performEncryption(template, operationId);
  
  check(null, {
    'endurance test encryption successful': () => success
  });
  
  // Update memory usage estimate
  memoryUsage.add(estimateMemoryUsage());
  
  // Consistent delay for endurance test
  sleep(0.1);
}

/**
 * Main test function
 */
export default async function (): Promise<void> {
  const scenario = __ENV.K6_SCENARIO || 'stress_test';
  
  switch (scenario) {
    case 'stress_test':
      await stressTest();
      break;
    case 'spike_test':
      await spikeTest();
      break;
    case 'endurance_test':
      await enduranceTest();
      break;
    default:
      // Default to stress test
      await stressTest();
  }
}

/**
 * Setup function
 */
export async function setup(): Promise<any> {
  console.log('=== Performance Test Setup ===');
  
  // Validate encryption utility
  try {
    const certInfo = parsePEMCertificate(TEST_CERTIFICATE);
    if (!certInfo.valid) {
      throw new Error('Test certificate is invalid');
    }
    
    // Perform a baseline encryption to ensure everything works
    const testPayload = generatePayload(PAYLOAD_TEMPLATES.standard, 0);
    const result = await payloadEncryptionFactoryWithErrors(testPayload, TEST_CERTIFICATE);
    
    if (!result || !result.success) {
      throw new Error('Baseline encryption test failed');
    }
    
    console.log('✅ Performance test setup completed successfully');
    console.log(`Certificate key size: ${certInfo.keySize} bits`);
    console.log(`Certificate algorithm: ${certInfo.algorithm}`);
    
    return {
      setupComplete: true,
      certificateInfo: {
        keySize: certInfo.keySize,
        algorithm: certInfo.algorithm,
        issuer: certInfo.issuer
      }
    };
  } catch (error) {
    console.log('❌ Performance test setup failed:', error);
    throw error;
  }
}

/**
 * Teardown function
 */
export function teardown(data: any): void {
  console.log('=== Performance Test Teardown ===');
  
  if (data && data.setupComplete) {
    console.log('✅ Performance test completed successfully');
    
    // Log final metrics summary
    console.log('Final Performance Metrics:');
    console.log('- Average encryption duration: Tracked via metrics');
    console.log('- 95th percentile duration: Tracked via metrics');
    console.log('- Success rate: Tracked via metrics');
    console.log('- Total errors: Tracked via metrics');
    console.log('- Peak concurrent operations: Tracked via metrics');
  } else {
    console.log('❌ Performance test teardown - setup was not completed');
  }
}