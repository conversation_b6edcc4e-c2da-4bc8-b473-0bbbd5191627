/**
 * Integration Test Runner
 * Orchestrates all integration and compatibility tests for the encryption utility
 */

import { check, sleep } from 'k6';
import { Options } from 'k6/options';
import { Counter, Rate, Trend, Gauge } from 'k6/metrics';

// Import test modules
import { runEncryptionTests } from './utils/encryption.test';

// Test configuration
export const options: Options = {
  scenarios: {
    // Full integration test suite
    full_integration: {
      executor: 'shared-iterations',
      vus: 1,
      iterations: 1,
      maxDuration: '120s',
      tags: { test_type: 'full_integration' }
    },
    // Performance validation
    performance_validation: {
      executor: 'constant-vus',
      vus: 5,
      duration: '60s',
      tags: { test_type: 'performance_validation' }
    },
    // Stress test
    stress_validation: {
      executor: 'ramping-vus',
      startVUs: 1,
      stages: [
        { duration: '10s', target: 10 },
        { duration: '30s', target: 20 },
        { duration: '10s', target: 1 }
      ],
      tags: { test_type: 'stress_validation' }
    }
  },
  thresholds: {
    'integration_test_success': ['rate>0.95'],
    'performance_test_success': ['rate>0.90'],
    'stress_test_success': ['rate>0.85'],
    'test_duration': ['p(95)<5000'], // 95% of tests complete within 5s
    'checks': ['rate>0.95']
  }
};

// Custom metrics
const integrationTestSuccess = new Rate('integration_test_success');
const performanceTestSuccess = new Rate('performance_test_success');
const stressTestSuccess = new Rate('stress_test_success');
const testDuration = new Trend('test_duration');
const testsExecuted = new Counter('tests_executed');
const testErrors = new Counter('test_errors');

/**
 * Execute unit tests
 */
async function executeUnitTests(): Promise<boolean> {
  console.log('=== Executing Unit Tests ===');
  
  const startTime = Date.now();
  let success = true;
  
  try {
    await runEncryptionTests();
    console.log('✅ Unit tests completed');
  } catch (error) {
    console.log(`❌ Unit tests failed: ${error}`);
    success = false;
    testErrors.add(1);
  }
  
  const duration = Date.now() - startTime;
  testDuration.add(duration);
  testsExecuted.add(1);
  
  return success;
}

/**
 * Execute integration workflow tests
 */
async function executeIntegrationWorkflowTests(): Promise<boolean> {
  console.log('=== Executing Integration Workflow Tests ===');
  
  const startTime = Date.now();
  let success = true;
  
  try {
    // Import and run integration tests
    const { default: integrationTest } = await import('./integration-test-encryption');
    
    // Execute the integration test
    integrationTest();
    
    console.log('✅ Integration workflow tests completed');
  } catch (error) {
    console.log(`❌ Integration workflow tests failed: ${error}`);
    success = false;
    testErrors.add(1);
  }
  
  const duration = Date.now() - startTime;
  testDuration.add(duration);
  testsExecuted.add(1);
  
  return success;
}

/**
 * Execute K6 compatibility tests
 */
async function executeK6CompatibilityTests(): Promise<boolean> {
  console.log('=== Executing K6 Compatibility Tests ===');
  
  const startTime = Date.now();
  let success = true;
  
  try {
    // Import and run K6 compatibility tests
    const { default: compatibilityTest } = await import('./k6-compatibility-test');
    
    // Execute the compatibility test
    await compatibilityTest();
    
    console.log('✅ K6 compatibility tests completed');
  } catch (error) {
    console.log(`❌ K6 compatibility tests failed: ${error}`);
    success = false;
    testErrors.add(1);
  }
  
  const duration = Date.now() - startTime;
  testDuration.add(duration);
  testsExecuted.add(1);
  
  return success;
}

/**
 * Execute performance tests
 */
async function executePerformanceTests(): Promise<boolean> {
  console.log('=== Executing Performance Tests ===');
  
  const startTime = Date.now();
  let success = true;
  
  try {
    // Import and run performance tests
    const { default: performanceTest } = await import('./performance-test-encryption');
    
    // Execute the performance test
    performanceTest();
    
    console.log('✅ Performance tests completed');
  } catch (error) {
    console.log(`❌ Performance tests failed: ${error}`);
    success = false;
    testErrors.add(1);
  }
  
  const duration = Date.now() - startTime;
  testDuration.add(duration);
  testsExecuted.add(1);
  
  return success;
}

/**
 * Execute concurrent encryption tests
 */
async function executeConcurrentEncryptionTests(): Promise<boolean> {
  console.log('=== Executing Concurrent Encryption Tests ===');
  
  const startTime = Date.now();
  let success = true;
  
  try {
    // Import encryption functions
    const { payloadEncryptionFactoryWithErrors } = await import('./utils/encryption');
    
    // Test certificate
    const testCertificate = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==-----END CERTIFICATE-----`;
    
    // Perform concurrent encryptions
    const concurrentOperations = [];
    const operationCount = 10;
    
    for (let i = 0; i < operationCount; i++) {
      const payload = {
        pin: "1234",
        cardId: "****************",
        cardType: "VISA",
        otp: "123456",
        operationId: i,
        timestamp: new Date().toISOString()
      };
      
      concurrentOperations.push(
        payloadEncryptionFactoryWithErrors(payload, testCertificate)
      );
    }
    
    // Wait for all operations to complete (simulate concurrent execution)
    let successCount = 0;
    let errorCount = 0;
    
    for (const operation of concurrentOperations) {
      try {
        const result = await operation;
        if (result && result.success) {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        errorCount++;
      }
    }
    
    const successRate = successCount / operationCount;
    
    const concurrentPassed = check(null, {
      'concurrent operations completed': () => (successCount + errorCount) === operationCount,
      'success rate >= 90%': () => successRate >= 0.9,
      'at least some operations succeeded': () => successCount > 0
    });
    
    if (concurrentPassed) {
      console.log(`✅ Concurrent encryption tests passed (${successCount}/${operationCount} successful)`);
    } else {
      console.log(`❌ Concurrent encryption tests failed (${successCount}/${operationCount} successful)`);
      success = false;
    }
    
  } catch (error) {
    console.log(`❌ Concurrent encryption tests failed: ${error}`);
    success = false;
    testErrors.add(1);
  }
  
  const duration = Date.now() - startTime;
  testDuration.add(duration);
  testsExecuted.add(1);
  
  return success;
}

/**
 * Main test execution function
 */
export default async function (): Promise<void> {
  const scenario = __ENV.K6_SCENARIO || 'full_integration';
  
  console.log(`\n=== Integration Test Runner ===`);
  console.log(`Scenario: ${scenario}`);
  console.log(`VU: ${__VU}, Iteration: ${__ITER}`);
  
  let testResults: boolean[] = [];

  switch (scenario) {
    case 'full_integration':
      console.log('Running full integration test suite...');
      
      // Execute all test suites
      testResults.push(await executeUnitTests());
      testResults.push(await executeIntegrationWorkflowTests());
      testResults.push(await executeK6CompatibilityTests());
      testResults.push(await executeConcurrentEncryptionTests());
      
      break;
      
    case 'performance_validation':
      console.log('Running performance validation tests...');
      
      testResults.push(await executePerformanceTests());
      testResults.push(await executeConcurrentEncryptionTests());
      
      // Add small delay for performance testing
      sleep(0.1);
      
      break;
      
    case 'stress_validation':
      console.log('Running stress validation tests...');
      
      // Focus on concurrent operations for stress testing
      testResults.push(await executeConcurrentEncryptionTests());
      
      // Minimal delay for stress testing
      sleep(0.05);
      
      break;
      
    default:
      console.log('Running default test suite...');
      testResults.push(await executeIntegrationWorkflowTests());
  }

  // Calculate overall success
  const successfulTests = testResults.filter(result => result).length;
  const totalTests = testResults.length;
  const overallSuccess = successfulTests === totalTests;
  
  // Record metrics based on scenario
  switch (scenario) {
    case 'full_integration':
      integrationTestSuccess.add(overallSuccess ? 1 : 0);
      break;
    case 'performance_validation':
      performanceTestSuccess.add(overallSuccess ? 1 : 0);
      break;
    case 'stress_validation':
      stressTestSuccess.add(overallSuccess ? 1 : 0);
      break;
  }

  // Final validation
  const finalCheck = check(null, {
    'all tests in scenario passed': () => overallSuccess,
    'at least one test executed': () => totalTests > 0,
    'no critical errors': () => true // Errors are tracked via add() calls
  });

  // Summary
  console.log(`\n=== Test Results Summary ===`);
  console.log(`Scenario: ${scenario}`);
  console.log(`Tests passed: ${successfulTests}/${totalTests}`);
  console.log(`Success rate: ${((successfulTests / totalTests) * 100).toFixed(1)}%`);
  console.log('Total tests executed: Multiple test suites');
  console.log('Total errors: Tracked via metrics');
  
  if (overallSuccess) {
    console.log('✅ All integration tests PASSED');
  } else {
    console.log('❌ Some integration tests FAILED');
  }
}

/**
 * Setup function
 */
export function setup(): any {
  console.log('Setting up integration test runner...');
  
  // Validate K6 environment
  const isK6Environment = typeof __VU !== 'undefined';
  
  if (isK6Environment) {
    console.log(`K6 environment detected (VU: ${__VU})`);
  } else {
    console.log('Warning: Not running in K6 environment');
  }
  
  return {
    setupComplete: true,
    isK6Environment,
    startTime: Date.now()
  };
}

/**
 * Teardown function
 */
export function teardown(data: any): void {
  console.log('\n=== Integration Test Runner Teardown ===');
  
  if (data && data.setupComplete) {
    const totalDuration = Date.now() - data.startTime;
    
    console.log('Final Test Metrics:');
    console.log(`- Total execution time: ${totalDuration}ms`);
    console.log('- Tests executed: Multiple test suites');
    console.log('- Test errors: Tracked via metrics');
    console.log('- Average test duration: Tracked via metrics');
    console.log('- Integration test success rate: Tracked via metrics');
    console.log('- Performance test success rate: Tracked via metrics');
    console.log('- Stress test success rate: Tracked via metrics');
    
    console.log('✅ Integration test runner completed');
  } else {
    console.log('❌ Integration test runner teardown - setup was not completed');
  }
}