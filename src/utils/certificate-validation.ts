/**
 * Certificate format validation utilities for K6 environment
 * Validates X.509 certificate formats and structures
 */

/**
 * PEM certificate format patterns
 */
const PEM_CERTIFICATE_HEADER = '-----BEGIN CERTIFICATE-----';
const PEM_CERTIFICATE_FOOTER = '-----END CERTIFICATE-----';
const PEM_CERTIFICATE_REGEX = /^-----BEGIN CERTIFICATE-----\s*([A-Za-z0-9+/=\s]+)\s*-----END CERTIFICATE-----$/;

/**
 * Validate if a string is in valid PEM certificate format
 * @param pemCertificate - Certificate string to validate
 * @returns True if valid PEM format, false otherwise
 */
export function validatePEMFormat(pemCertificate: string): boolean {
  if (!pemCertificate || typeof pemCertificate !== 'string') {
    return false;
  }

  // Remove extra whitespace and normalize line endings
  const normalizedCert = pemCertificate.trim().replace(/\r\n/g, '\n');

  // Check for required headers and footers
  if (!normalizedCert.includes(PEM_CERTIFICATE_HEADER) || 
      !normalizedCert.includes(PEM_CERTIFICATE_FOOTER)) {
    return false;
  }

  // Validate overall PEM structure with regex
  return PEM_CERTIFICATE_REGEX.test(normalizedCert);
}

/**
 * Extract the base64 content from a PEM certificate
 * @param pemCertificate - PEM formatted certificate
 * @returns Base64 content without headers/footers, or null if invalid
 */
export function extractPEMContent(pemCertificate: string): string | null {
  if (!validatePEMFormat(pemCertificate)) {
    return null;
  }

  const normalizedCert = pemCertificate.trim().replace(/\r\n/g, '\n');
  const match = normalizedCert.match(PEM_CERTIFICATE_REGEX);
  
  if (!match || !match[1]) {
    return null;
  }

  // Remove whitespace from base64 content
  return match[1].replace(/\s/g, '');
}

/**
 * Validate certificate content structure (basic ASN.1 validation)
 * @param base64Content - Base64 encoded certificate content
 * @returns True if content appears to be valid ASN.1 structure
 */
export function validateCertificateContent(base64Content: string): boolean {
  try {
    // Decode base64 to check if it's valid
    const binaryData = atob(base64Content);
    
    // Basic ASN.1 structure check - should start with SEQUENCE tag (0x30)
    if (binaryData.length < 4) {
      return false;
    }

    const firstByte = binaryData.charCodeAt(0);
    
    // ASN.1 SEQUENCE tag is 0x30
    if (firstByte !== 0x30) {
      return false;
    }

    // Check if length encoding is reasonable
    const secondByte = binaryData.charCodeAt(1);
    
    // If length is in long form (bit 7 set), validate structure
    if (secondByte & 0x80) {
      const lengthBytes = secondByte & 0x7F;
      if (lengthBytes === 0 || lengthBytes > 4) {
        return false; // Invalid length encoding
      }
      
      if (binaryData.length < 2 + lengthBytes) {
        return false; // Not enough data for length bytes
      }
    }

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Comprehensive certificate format validation
 * @param certificate - Certificate string to validate
 * @returns Validation result with details
 */
export interface CertificateValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export function validateCertificateFormat(certificate: string): CertificateValidationResult {
  const result: CertificateValidationResult = {
    isValid: true,
    errors: [],
    warnings: []
  };

  // Check if certificate is provided
  if (!certificate) {
    result.isValid = false;
    result.errors.push('Certificate is required');
    return result;
  }

  // Check if it's a string
  if (typeof certificate !== 'string') {
    result.isValid = false;
    result.errors.push('Certificate must be a string');
    return result;
  }

  // Validate PEM format
  if (!validatePEMFormat(certificate)) {
    result.isValid = false;
    result.errors.push('Invalid PEM certificate format');
    return result;
  }

  // Extract and validate content
  const content = extractPEMContent(certificate);
  if (!content) {
    result.isValid = false;
    result.errors.push('Could not extract certificate content');
    return result;
  }

  // Validate ASN.1 structure
  if (!validateCertificateContent(content)) {
    result.isValid = false;
    result.errors.push('Invalid certificate content structure');
    return result;
  }

  // Check certificate length (reasonable bounds)
  if (content.length < 100) {
    result.warnings.push('Certificate appears unusually short');
  } else if (content.length > 10000) {
    result.warnings.push('Certificate appears unusually long');
  }

  return result;
}

/**
 * Check if certificate string contains multiple certificates
 * @param certificateString - String that might contain multiple certificates
 * @returns Array of individual certificate strings
 */
export function splitMultipleCertificates(certificateString: string): string[] {
  if (!certificateString) {
    return [];
  }

  const certificates: string[] = [];
  const headerPattern = /-----BEGIN CERTIFICATE-----/g;
  const footerPattern = /-----END CERTIFICATE-----/g;

  let headerMatch;
  let footerMatch;

  while ((headerMatch = headerPattern.exec(certificateString)) !== null) {
    footerPattern.lastIndex = headerMatch.index;
    footerMatch = footerPattern.exec(certificateString);

    if (footerMatch) {
      const cert = certificateString.substring(
        headerMatch.index,
        footerMatch.index + footerMatch[0].length
      );
      certificates.push(cert.trim());
      headerPattern.lastIndex = footerMatch.index + footerMatch[0].length;
    } else {
      break; // No matching footer found
    }
  }

  return certificates;
}