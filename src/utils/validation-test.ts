/**
 * Test script to validate the input validation and error handling functionality
 */

import {
  validatePayloadInput,
  validateCertificateInput,
  validateEncryptionInputs,
  comprehensiveValidation,
  EncryptionErrorType,
  formatEncryptionError,
  validatePayloadSafety,
  handleCircularReferences
} from './encryption';

/**
 * Test cases for input validation
 */
export function runValidationTests(): void {
  console.log('=== Running Input Validation Tests ===\n');

  // Test 1: Null/undefined payload validation
  console.log('Test 1: Null/undefined payload validation');
  const nullPayloadResult = validatePayloadInput(null);
  console.log('Null payload result:', nullPayloadResult);
  console.log('Expected error code:', EncryptionErrorType.INVALID_PAYLOAD);
  console.log('Match:', nullPayloadResult.error?.code === EncryptionErrorType.INVALID_PAYLOAD);
  console.log('');

  // Test 2: Empty object validation
  console.log('Test 2: Empty object validation');
  const emptyObjectResult = validatePayloadInput({});
  console.log('Empty object result:', emptyObjectResult);
  console.log('Expected error code:', EncryptionErrorType.INVALID_PAYLOAD);
  console.log('Match:', emptyObjectResult.error?.code === EncryptionErrorType.INVALID_PAYLOAD);
  console.log('');

  // Test 3: Empty string validation
  console.log('Test 3: Empty string validation');
  const emptyStringResult = validatePayloadInput('   ');
  console.log('Empty string result:', emptyStringResult);
  console.log('Expected error code:', EncryptionErrorType.INVALID_PAYLOAD);
  console.log('Match:', emptyStringResult.error?.code === EncryptionErrorType.INVALID_PAYLOAD);
  console.log('');

  // Test 4: Valid payload validation
  console.log('Test 4: Valid payload validation');
  const validPayload = { pin: '1234', cardId: 'ABC123', cardType: 'DEBIT', otp: '567890' };
  const validPayloadResult = validatePayloadInput(validPayload);
  console.log('Valid payload result:', validPayloadResult);
  console.log('Expected success:', true);
  console.log('Match:', validPayloadResult.success === true);
  console.log('');

  // Test 5: Certificate validation - null/empty
  console.log('Test 5: Certificate validation - null/empty');
  const emptyCertResult = validateCertificateInput('');
  console.log('Empty certificate result:', emptyCertResult);
  console.log('Expected error code:', EncryptionErrorType.INVALID_CERTIFICATE);
  console.log('Match:', emptyCertResult.error?.code === EncryptionErrorType.INVALID_CERTIFICATE);
  console.log('');

  // Test 6: Certificate validation - wrong type
  console.log('Test 6: Certificate validation - wrong type');
  const wrongTypeCertResult = validateCertificateInput(123 as any);
  console.log('Wrong type certificate result:', wrongTypeCertResult);
  console.log('Expected error code:', EncryptionErrorType.INVALID_CERTIFICATE);
  console.log('Match:', wrongTypeCertResult.error?.code === EncryptionErrorType.INVALID_CERTIFICATE);
  console.log('');

  // Test 7: Circular reference handling
  console.log('Test 7: Circular reference handling');
  const circularObj: any = { name: 'test' };
  circularObj.self = circularObj;
  
  try {
    const handled = handleCircularReferences(circularObj);
    console.log('Circular reference handled:', handled);
    console.log('Success: Circular reference was handled without error');
  } catch (error) {
    console.log('Error handling circular reference:', error);
  }
  console.log('');

  // Test 8: Payload safety validation
  console.log('Test 8: Payload safety validation');
  const unsafePayload = { script: '<script>alert("xss")</script>', data: 'normal' };
  const safetyResult = validatePayloadSafety(unsafePayload);
  console.log('Unsafe payload safety result:', safetyResult);
  console.log('Expected safe:', false);
  console.log('Match:', safetyResult.isSafe === false);
  console.log('');

  // Test 9: Large payload validation
  console.log('Test 9: Large payload validation');
  const largePayload = { data: 'x'.repeat(15000) }; // Large but not too large
  const largePayloadResult = validatePayloadInput(largePayload);
  console.log('Large payload result success:', largePayloadResult.success);
  
  const veryLargePayload = { data: 'x'.repeat(50000) }; // Very large payload
  const veryLargePayloadResult = validatePayloadInput(veryLargePayload);
  console.log('Very large payload result:', veryLargePayloadResult);
  console.log('Expected error code:', EncryptionErrorType.PAYLOAD_TOO_LARGE);
  console.log('Match:', veryLargePayloadResult.error?.code === EncryptionErrorType.PAYLOAD_TOO_LARGE);
  console.log('');

  // Test 10: Comprehensive validation
  console.log('Test 10: Comprehensive validation');
  const comprehensiveResult = comprehensiveValidation(validPayload, 'invalid-cert');
  console.log('Comprehensive validation result:', comprehensiveResult);
  console.log('Expected valid:', false);
  console.log('Match:', comprehensiveResult.isValid === false);
  console.log('Errors found:', comprehensiveResult.errors.length > 0);
  console.log('');

  // Test 11: Error formatting
  console.log('Test 11: Error formatting');
  if (nullPayloadResult.error) {
    const formattedError = formatEncryptionError(nullPayloadResult.error);
    console.log('Formatted error:', formattedError);
    console.log('Contains error code:', formattedError.includes(EncryptionErrorType.INVALID_PAYLOAD));
  }
  console.log('');

  console.log('=== Validation Tests Complete ===');
}

// Run tests if this file is executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runValidationTests();
}