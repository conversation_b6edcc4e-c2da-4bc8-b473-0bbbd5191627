/**
 * K6-compatible utility functions for encryption operations
 * These functions avoid Node.js-specific APIs and work within K6's JavaScript runtime
 */

// Import K6's encoding module for base64 operations
import { b64encode } from 'k6/encoding';

/**
 * Base64 encoding function compatible with K6 environment
 * Uses K6's encoding module instead of browser btoa
 * @param data - Uint8Array data to encode
 * @returns Base64 encoded string
 */
export function encodeToBase64(data: Uint8Array): string {
  // Convert Uint8Array to binary string
  let binaryString = '';
  for (let i = 0; i < data.length; i++) {
    binaryString += String.fromCharCode(data[i]);
  }
  
  // Use K6's b64encode function instead of btoa
  return b64encode(binaryString);
}

/**
 * Base64 decoding function compatible with K6 environment
 * @param base64String - Base64 encoded string to decode
 * @returns Uint8Array of decoded data
 */
export function decodeFromBase64(base64String: string): Uint8Array {
  // Import K6's b64decode function
  const { b64decode } = require('k6/encoding');
  
  // Use K6's b64decode function instead of atob
  const binaryString = b64decode(base64String);
  
  // Convert binary string to Uint8Array
  const uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }
  
  return uint8Array;
}

/**
 * Convert string to Uint8Array using UTF-8 encoding
 * K6-compatible implementation without TextEncoder dependency
 * @param str - String to convert
 * @returns Uint8Array representation of the string
 */
export function stringToUint8Array(str: string): Uint8Array {
  // Manual UTF-8 encoding implementation for K6 compatibility
  const utf8Bytes: number[] = [];
  
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    
    if (charCode < 0x80) {
      // 1-byte character (ASCII)
      utf8Bytes.push(charCode);
    } else if (charCode < 0x800) {
      // 2-byte character
      utf8Bytes.push(0xC0 | (charCode >> 6));
      utf8Bytes.push(0x80 | (charCode & 0x3F));
    } else if (charCode < 0xD800 || charCode >= 0xE000) {
      // 3-byte character (not surrogate)
      utf8Bytes.push(0xE0 | (charCode >> 12));
      utf8Bytes.push(0x80 | ((charCode >> 6) & 0x3F));
      utf8Bytes.push(0x80 | (charCode & 0x3F));
    } else {
      // 4-byte character (surrogate pair)
      if (i + 1 < str.length) {
        const nextCharCode = str.charCodeAt(i + 1);
        if (charCode >= 0xD800 && charCode <= 0xDBFF && nextCharCode >= 0xDC00 && nextCharCode <= 0xDFFF) {
          // Valid surrogate pair
          const codePoint = 0x10000 + ((charCode & 0x3FF) << 10) + (nextCharCode & 0x3FF);
          utf8Bytes.push(0xF0 | (codePoint >> 18));
          utf8Bytes.push(0x80 | ((codePoint >> 12) & 0x3F));
          utf8Bytes.push(0x80 | ((codePoint >> 6) & 0x3F));
          utf8Bytes.push(0x80 | (codePoint & 0x3F));
          i++; // Skip next character as it's part of surrogate pair
        } else {
          // Invalid surrogate, replace with replacement character
          utf8Bytes.push(0xEF, 0xBF, 0xBD);
        }
      } else {
        // Invalid surrogate at end of string, replace with replacement character
        utf8Bytes.push(0xEF, 0xBF, 0xBD);
      }
    }
  }
  
  return new Uint8Array(utf8Bytes);
}

/**
 * Convert Uint8Array to string using UTF-8 decoding
 * K6-compatible implementation without TextDecoder dependency
 * @param uint8Array - Uint8Array to convert
 * @returns String representation
 */
export function uint8ArrayToString(uint8Array: Uint8Array): string {
  // Manual UTF-8 decoding implementation for K6 compatibility
  let result = '';
  let i = 0;
  
  while (i < uint8Array.length) {
    const byte1 = uint8Array[i];
    
    if (byte1 < 0x80) {
      // 1-byte character (ASCII)
      result += String.fromCharCode(byte1);
      i++;
    } else if ((byte1 & 0xE0) === 0xC0) {
      // 2-byte character
      if (i + 1 >= uint8Array.length) break;
      const byte2 = uint8Array[i + 1];
      const charCode = ((byte1 & 0x1F) << 6) | (byte2 & 0x3F);
      result += String.fromCharCode(charCode);
      i += 2;
    } else if ((byte1 & 0xF0) === 0xE0) {
      // 3-byte character
      if (i + 2 >= uint8Array.length) break;
      const byte2 = uint8Array[i + 1];
      const byte3 = uint8Array[i + 2];
      const charCode = ((byte1 & 0x0F) << 12) | ((byte2 & 0x3F) << 6) | (byte3 & 0x3F);
      result += String.fromCharCode(charCode);
      i += 3;
    } else if ((byte1 & 0xF8) === 0xF0) {
      // 4-byte character (surrogate pair)
      if (i + 3 >= uint8Array.length) break;
      const byte2 = uint8Array[i + 1];
      const byte3 = uint8Array[i + 2];
      const byte4 = uint8Array[i + 3];
      const codePoint = ((byte1 & 0x07) << 18) | ((byte2 & 0x3F) << 12) | ((byte3 & 0x3F) << 6) | (byte4 & 0x3F);
      
      if (codePoint >= 0x10000) {
        // Convert to surrogate pair
        const adjusted = codePoint - 0x10000;
        const high = 0xD800 + (adjusted >> 10);
        const low = 0xDC00 + (adjusted & 0x3FF);
        result += String.fromCharCode(high, low);
      } else {
        result += String.fromCharCode(codePoint);
      }
      i += 4;
    } else {
      // Invalid byte, skip
      i++;
    }
  }
  
  return result;
}

/**
 * Convert hex string to Uint8Array
 * @param hexString - Hexadecimal string to convert
 * @returns Uint8Array representation
 */
export function hexToUint8Array(hexString: string): Uint8Array {
  // Remove any whitespace and ensure even length
  const cleanHex = hexString.replace(/\s/g, '');
  if (cleanHex.length % 2 !== 0) {
    throw new Error('Invalid hex string: length must be even');
  }
  
  const uint8Array = new Uint8Array(cleanHex.length / 2);
  for (let i = 0; i < cleanHex.length; i += 2) {
    uint8Array[i / 2] = parseInt(cleanHex.substring(i, i + 2), 16);
  }
  
  return uint8Array;
}

/**
 * Convert Uint8Array to hex string
 * @param uint8Array - Uint8Array to convert
 * @returns Hexadecimal string representation
 */
export function uint8ArrayToHex(uint8Array: Uint8Array): string {
  return Array.from(uint8Array)
    .map(byte => byte.toString(16).padStart(2, '0'))
    .join('');
}