# K6 Encryption Utility

This directory contains the K6-compatible encryption utility that provides RSA encryption functionality for JSON payloads using X.509 certificates.

## Documentation

📖 **[Complete API Reference](../../docs/encryption-utility-api.md)** - Detailed API documentation with all functions and interfaces

📋 **[Usage Examples](../../docs/encryption-utility-examples.md)** - Comprehensive examples for different scenarios

🔧 **[Troubleshooting Guide](../../docs/encryption-utility-troubleshooting.md)** - Solutions for common issues

## Quick Start

### Basic Usage

```typescript
import { payloadEncryptionFactory } from './utils/encryption';

const payload = {
  pin: "1234",
  cardId: "****************",
  cardType: "VISA",
  otp: "123456"
};

const certificate = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV...
-----<PERSON><PERSON>RTIFICATE-----`;

const encrypted = await payloadEncryptionFactory(payload, certificate);
if (encrypted) {
  console.log('Encrypted payload:', encrypted);
}
```

### Enhanced Usage with Error Handling

```typescript
import { 
  payloadEncryptionFactoryWithErrors,
  formatEncryptionError 
} from './utils/encryption';

const result = await payloadEncryptionFactoryWithErrors(payload, certificate);
if (result.success) {
  console.log('Encrypted:', result.data);
} else {
  console.error('Error:', formatEncryptionError(result.error));
}
```

## Files Overview

### Core Encryption Module
- **`encryption.ts`** - Main encryption functionality with RSA-OAEP implementation
- **`certificate-validation.ts`** - X.509 certificate format validation utilities  
- **`k6-utils.ts`** - K6-compatible utility functions (base64, string conversions)
- **`index.ts`** - Main exports for easy importing

### Test Files
- **`encryption.test.ts`** - Comprehensive unit tests
- **`validation-test.ts`** - Input validation tests
- **`certificate-validation.ts`** - Certificate validation tests

## Key Functions

### Main Encryption Functions
```typescript
// Basic encryption (returns null on failure)
payloadEncryptionFactory(payload: any, certificate: string): Promise<string | null>

// Enhanced encryption (returns detailed error information)
payloadEncryptionFactoryWithErrors(payload: any, certificate: string): Promise<EnhancedEncryptionResult>
```

### Validation Functions
```typescript
// Comprehensive input validation
validateEncryptionInputs(payload: any, certificate: string): EnhancedEncryptionResult

// Individual validations
validatePayloadInput(payload: any): EnhancedEncryptionResult
validateCertificateInput(certificate: string): EnhancedEncryptionResult
```

### Utility Functions
```typescript
// Certificate parsing
parsePEMCertificate(pemCertificate: string): CertificateInfo

// Error formatting
formatEncryptionError(error: EncryptionError): string

// Safety validation
validatePayloadSafety(payload: any): { isSafe: boolean; issues: string[] }
```

## Examples

### K6 Script Examples

- **[Basic Example](../../examples/basic-encryption-example.js)** - Simple encryption in K6 load test
- **[Advanced Example](../../examples/advanced-encryption-example.js)** - Advanced usage with error handling and metrics

### Running Examples

```bash
# Basic example
k6 run examples/basic-encryption-example.js

# Advanced example with custom certificate
ENCRYPTION_CERTIFICATE="$(cat your-cert.pem)" k6 run examples/advanced-encryption-example.js

# With debug mode
DEBUG_MODE=true k6 run examples/advanced-encryption-example.js
```

## K6 Compatibility

This utility is designed to work within K6's JavaScript runtime (v1.1.1) and:
- ✅ Uses `jsrsasign` library for cryptographic operations
- ✅ Avoids Node.js-specific APIs like Buffer
- ✅ Uses K6-compatible base64 encoding with `btoa/atob`
- ✅ Handles binary data with Uint8Array
- ✅ Uses TextEncoder/TextDecoder for string conversions
- ✅ No external dependencies beyond jsrsasign

## Security Features

- **RSA-OAEP Encryption**: Uses OAEP padding for semantic security
- **Certificate Validation**: Validates X.509 certificate format and expiration
- **Input Sanitization**: Comprehensive input validation and sanitization
- **Size Limits**: Enforces payload size limits based on key size
- **Error Handling**: Structured error reporting without exposing sensitive data

## Performance Considerations

- **Key Size Limits**: 
  - RSA-2048: ~214 bytes max payload
  - RSA-3072: ~342 bytes max payload
  - RSA-4096: ~470 bytes max payload
- **Encryption Time**: Typically 50-200ms per operation
- **Memory Usage**: Minimal memory footprint
- **Concurrent Operations**: Supports high concurrency in K6

## Dependencies

- `jsrsasign` - RSA cryptographic operations and X.509 certificate parsing
- `@types/jsrsasign` - TypeScript type definitions

## Support

For issues and questions:
1. Check the [Troubleshooting Guide](../../docs/encryption-utility-troubleshooting.md)
2. Review the [API Documentation](../../docs/encryption-utility-api.md)
3. Look at the [Usage Examples](../../docs/encryption-utility-examples.md)