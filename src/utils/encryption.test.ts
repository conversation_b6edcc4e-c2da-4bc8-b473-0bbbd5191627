/**
 * Comprehensive unit tests for encryption utility
 * Compatible with K6 environment and standard testing frameworks
 */

import {
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate,
  encryptWithRSAOAEP,
  validatePayloadSize,
  validateEncryptionInputs,
  validatePayloadInput,
  validateCertificateInput,
  validateParsedCertificate,
  comprehensiveValidation,
  EncryptionErrorType,
  formatEncryptionError,
  validatePayloadSafety,
  handleCircularReferences,
  sanitizeInputs,
  validateCertificateKeyType,
  validateCertificateExpiration,
  type CertificateInfo,
  type EncryptionError,
  type EnhancedEncryptionResult
} from './encryption';

// Test certificate (RSA 2048-bit, valid until 2030)
const VALID_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==-----END CERTIFICATE-----`;

// Invalid certificate formats for testing
const INVALID_CERTIFICATES = {
  empty: '',
  whitespace: '   \n\t  ',
  wrongType: 123 as any,
  missingHeader: `MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
-----END CERTIFICATE-----`,
  missingFooter: `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV`,
  invalidContent: `-----BEGIN CERTIFICATE-----
invalid-base64-content!@#$%
-----END CERTIFICATE-----`
};

// Test payloads
const TEST_PAYLOADS = {
  valid: {
    pin: "1234",
    cardId: "****************",
    cardType: "VISA",
    otp: "123456"
  },
  empty: {},
  null: null,
  undefined: undefined,
  emptyString: '',
  whitespaceString: '   ',
  emptyArray: [],
  large: {
    data: 'x'.repeat(10000)
  },
  circular: (() => {
    const obj: any = { name: 'test' };
    obj.self = obj;
    return obj;
  })(),
  withScript: {
    data: '<script>alert("xss")</script>',
    normal: 'value'
  }
};

/**
 * Simple test framework for K6 compatibility
 */
class TestSuite {
  private tests: Array<{ name: string; fn: () => void | Promise<void> }> = [];
  private results: Array<{ name: string; passed: boolean; error?: any }> = [];

  test(name: string, fn: () => void | Promise<void>): void {
    this.tests.push({ name, fn });
  }

  async run(): Promise<void> {
    console.log(`\n=== Running ${this.tests.length} tests ===`);
    
    for (const test of this.tests) {
      try {
        await test.fn();
        this.results.push({ name: test.name, passed: true });
        console.log(`✅ ${test.name}`);
      } catch (error) {
        this.results.push({ name: test.name, passed: false, error });
        console.log(`❌ ${test.name}: ${error}`);
      }
    }
    
    this.printSummary();
  }

  private printSummary(): void {
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    
    console.log(`\n=== Test Summary ===`);
    console.log(`Passed: ${passed}/${total}`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
    
    const failed = this.results.filter(r => !r.passed);
    if (failed.length > 0) {
      console.log(`\nFailed Tests:`);
      failed.forEach(f => {
        console.log(`- ${f.name}: ${f.error}`);
      });
    }
  }

  expect(actual: any): {
    toBe: (expected: any) => void;
    toEqual: (expected: any) => void;
    toBeTruthy: () => void;
    toBeFalsy: () => void;
    toBeNull: () => void;
    toBeUndefined: () => void;
    toContain: (expected: any) => void;
    toThrow: () => void;
    toBeInstanceOf: (constructor: any) => void;
  } {
    return {
      toBe: (expected: any) => {
        if (actual !== expected) {
          throw new Error(`Expected ${actual} to be ${expected}`);
        }
      },
      toEqual: (expected: any) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
        }
      },
      toBeTruthy: () => {
        if (!actual) {
          throw new Error(`Expected ${actual} to be truthy`);
        }
      },
      toBeFalsy: () => {
        if (actual) {
          throw new Error(`Expected ${actual} to be falsy`);
        }
      },
      toBeNull: () => {
        if (actual !== null) {
          throw new Error(`Expected ${actual} to be null`);
        }
      },
      toBeUndefined: () => {
        if (actual !== undefined) {
          throw new Error(`Expected ${actual} to be undefined`);
        }
      },
      toContain: (expected: any) => {
        if (!actual || !actual.includes || !actual.includes(expected)) {
          throw new Error(`Expected ${actual} to contain ${expected}`);
        }
      },
      toThrow: () => {
        let threw = false;
        try {
          if (typeof actual === 'function') {
            actual();
          }
        } catch (e) {
          threw = true;
        }
        if (!threw) {
          throw new Error('Expected function to throw');
        }
      },
      toBeInstanceOf: (constructor: any) => {
        if (!(actual instanceof constructor)) {
          throw new Error(`Expected ${actual} to be instance of ${constructor.name}`);
        }
      }
    };
  }
}

/**
 * Certificate parsing tests
 */
function createCertificateParsingTests(suite: TestSuite): void {
  suite.test('parsePEMCertificate - valid certificate', () => {
    const result = parsePEMCertificate(VALID_CERTIFICATE);
    
    suite.expect(result.valid).toBeTruthy();
    suite.expect(result.publicKey).toBeTruthy();
    suite.expect(result.algorithm).toBe('RSA');
    suite.expect(result.keySize).toBe(2048);
    suite.expect(result.isExpired).toBeFalsy();
  });

  suite.test('parsePEMCertificate - empty certificate', () => {
    const result = parsePEMCertificate(INVALID_CERTIFICATES.empty);
    
    suite.expect(result.valid).toBeFalsy();
    suite.expect(result.publicKey).toBeNull();
  });

  suite.test('parsePEMCertificate - invalid format', () => {
    const result = parsePEMCertificate(INVALID_CERTIFICATES.invalidContent);
    
    suite.expect(result.valid).toBeFalsy();
    suite.expect(result.publicKey).toBeNull();
  });

  suite.test('validateCertificateKeyType - valid RSA key', () => {
    const certInfo = parsePEMCertificate(VALID_CERTIFICATE);
    if (certInfo.valid && certInfo.publicKey) {
      const result = validateCertificateKeyType(null, certInfo.publicKey);
      
      suite.expect(result.isValid).toBeTruthy();
      suite.expect(result.keyType).toBe('RSA');
      suite.expect(result.keySize).toBe(2048);
    }
  });
}

/**
 * Input validation tests
 */
function createInputValidationTests(suite: TestSuite): void {
  suite.test('validatePayloadInput - valid payload', () => {
    const result = validatePayloadInput(TEST_PAYLOADS.valid);
    
    suite.expect(result.success).toBeTruthy();
    suite.expect(result.data).toBeTruthy();
  });

  suite.test('validatePayloadInput - null payload', () => {
    const result = validatePayloadInput(TEST_PAYLOADS.null);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_PAYLOAD);
  });

  suite.test('validatePayloadInput - empty object', () => {
    const result = validatePayloadInput(TEST_PAYLOADS.empty);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_PAYLOAD);
  });

  suite.test('validatePayloadInput - empty string', () => {
    const result = validatePayloadInput(TEST_PAYLOADS.emptyString);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_PAYLOAD);
  });

  suite.test('validatePayloadInput - large payload', () => {
    const result = validatePayloadInput(TEST_PAYLOADS.large);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.PAYLOAD_TOO_LARGE);
  });

  suite.test('validateCertificateInput - valid certificate', () => {
    const result = validateCertificateInput(VALID_CERTIFICATE);
    
    suite.expect(result.success).toBeTruthy();
  });

  suite.test('validateCertificateInput - empty certificate', () => {
    const result = validateCertificateInput(INVALID_CERTIFICATES.empty);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_CERTIFICATE);
  });

  suite.test('validateCertificateInput - wrong type', () => {
    const result = validateCertificateInput(INVALID_CERTIFICATES.wrongType);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_CERTIFICATE);
  });
}

/**
 * Encryption function tests
 */
function createEncryptionTests(suite: TestSuite): void {
  suite.test('encryptWithRSAOAEP - valid inputs', () => {
    const certInfo = parsePEMCertificate(VALID_CERTIFICATE);
    if (certInfo.valid && certInfo.publicKey) {
      const message = JSON.stringify(TEST_PAYLOADS.valid);
      const result = encryptWithRSAOAEP(message, certInfo.publicKey);
      
      suite.expect(result).toBeTruthy();
      if (result) {
        // Validate base64 format
        suite.expect(/^[A-Za-z0-9+/]*={0,2}$/.test(result)).toBeTruthy();
        // Validate length (RSA 2048 should produce ~344 chars)
        suite.expect(result.length >= 340 && result.length <= 350).toBeTruthy();
      }
    }
  });

  suite.test('encryptWithRSAOAEP - null message', () => {
    const certInfo = parsePEMCertificate(VALID_CERTIFICATE);
    if (certInfo.valid && certInfo.publicKey) {
      const result = encryptWithRSAOAEP(null as any, certInfo.publicKey);
      
      suite.expect(result).toBeNull();
    }
  });

  suite.test('encryptWithRSAOAEP - null public key', () => {
    const message = JSON.stringify(TEST_PAYLOADS.valid);
    const result = encryptWithRSAOAEP(message, null);
    
    suite.expect(result).toBeNull();
  });

  suite.test('validatePayloadSize - valid size', () => {
    const result = validatePayloadSize(100, 2048);
    
    suite.expect(result).toBeTruthy();
  });

  suite.test('validatePayloadSize - too large', () => {
    const result = validatePayloadSize(1000, 2048);
    
    suite.expect(result).toBeFalsy();
  });
}

/**
 * Enhanced encryption function tests
 */
function createEnhancedEncryptionTests(suite: TestSuite): void {
  suite.test('payloadEncryptionFactoryWithErrors - valid inputs', async () => {
    const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOADS.valid, VALID_CERTIFICATE);
    
    suite.expect(result.success).toBeTruthy();
    suite.expect(result.data).toBeTruthy();
    if (result.data) {
      suite.expect(/^[A-Za-z0-9+/]*={0,2}$/.test(result.data)).toBeTruthy();
    }
  });

  suite.test('payloadEncryptionFactoryWithErrors - null payload', async () => {
    const result = await payloadEncryptionFactoryWithErrors(null, VALID_CERTIFICATE);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_PAYLOAD);
  });

  suite.test('payloadEncryptionFactoryWithErrors - invalid certificate', async () => {
    const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOADS.valid, INVALID_CERTIFICATES.empty);
    
    suite.expect(result.success).toBeFalsy();
    suite.expect(result.error?.code).toBe(EncryptionErrorType.INVALID_CERTIFICATE);
  });

  suite.test('payloadEncryptionFactory - valid inputs', async () => {
    const result = await payloadEncryptionFactory(TEST_PAYLOADS.valid, VALID_CERTIFICATE);
    
    suite.expect(result).toBeTruthy();
    if (result) {
      suite.expect(/^[A-Za-z0-9+/]*={0,2}$/.test(result)).toBeTruthy();
    }
  });

  suite.test('payloadEncryptionFactory - invalid inputs', async () => {
    const result = await payloadEncryptionFactory(null, VALID_CERTIFICATE);
    
    suite.expect(result).toBeNull();
  });
}

/**
 * Utility function tests
 */
function createUtilityTests(suite: TestSuite): void {
  suite.test('handleCircularReferences - circular object', () => {
    const result = handleCircularReferences(TEST_PAYLOADS.circular);
    
    suite.expect(result.name).toBe('test');
    suite.expect(result.self).toBe('[Circular Reference]');
  });

  suite.test('validatePayloadSafety - safe payload', () => {
    const result = validatePayloadSafety(TEST_PAYLOADS.valid);
    
    suite.expect(result.isSafe).toBeTruthy();
    suite.expect(result.issues.length).toBe(0);
  });

  suite.test('validatePayloadSafety - unsafe payload', () => {
    const result = validatePayloadSafety(TEST_PAYLOADS.withScript);
    
    suite.expect(result.isSafe).toBeFalsy();
    suite.expect(result.issues.length > 0).toBeTruthy();
  });

  suite.test('sanitizeInputs - normal inputs', () => {
    const result = sanitizeInputs(TEST_PAYLOADS.valid, VALID_CERTIFICATE);
    
    suite.expect(result.payload).toEqual(TEST_PAYLOADS.valid);
    suite.expect(result.certificate).toBe(VALID_CERTIFICATE);
  });

  suite.test('formatEncryptionError - basic error', () => {
    const error: EncryptionError = {
      code: EncryptionErrorType.INVALID_PAYLOAD,
      message: 'Test error'
    };
    
    const result = formatEncryptionError(error);
    
    suite.expect(result).toContain('[INVALID_PAYLOAD]');
    suite.expect(result).toContain('Test error');
  });

  suite.test('formatEncryptionError - error with details', () => {
    const error: EncryptionError = {
      code: EncryptionErrorType.PAYLOAD_TOO_LARGE,
      message: 'Payload too large',
      details: { size: 1000, maxSize: 500 }
    };
    
    const result = formatEncryptionError(error);
    
    suite.expect(result).toContain('[PAYLOAD_TOO_LARGE]');
    suite.expect(result).toContain('Details:');
  });
}

/**
 * Comprehensive validation tests
 */
function createComprehensiveValidationTests(suite: TestSuite): void {
  suite.test('comprehensiveValidation - valid inputs', () => {
    const result = comprehensiveValidation(TEST_PAYLOADS.valid, VALID_CERTIFICATE);
    
    suite.expect(result.isValid).toBeTruthy();
    suite.expect(result.errors.length).toBe(0);
  });

  suite.test('comprehensiveValidation - invalid inputs', () => {
    const result = comprehensiveValidation(null, INVALID_CERTIFICATES.empty);
    
    suite.expect(result.isValid).toBeFalsy();
    suite.expect(result.errors.length > 0).toBeTruthy();
  });

  suite.test('comprehensiveValidation - unsafe payload', () => {
    const result = comprehensiveValidation(TEST_PAYLOADS.withScript, VALID_CERTIFICATE);
    
    suite.expect(result.safetyIssues.length > 0).toBeTruthy();
  });
}

/**
 * Main test runner
 */
export async function runEncryptionTests(): Promise<void> {
  const suite = new TestSuite();

  // Add all test suites
  createCertificateParsingTests(suite);
  createInputValidationTests(suite);
  createEncryptionTests(suite);
  createEnhancedEncryptionTests(suite);
  createUtilityTests(suite);
  createComprehensiveValidationTests(suite);

  // Run all tests
  await suite.run();
}

/**
 * K6 compatible test function
 */
export default function (): void {
  console.log('=== K6 Encryption Utility Unit Tests ===');
  
  // Run tests synchronously for K6 compatibility
  runEncryptionTests().catch(error => {
    console.log('Test execution failed:', error);
  });
}

// Run tests if executed directly (not in K6)
if (typeof __VU === 'undefined') {
  runEncryptionTests().catch(console.error);
}