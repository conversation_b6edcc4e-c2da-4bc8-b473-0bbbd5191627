/**
 * K6-compatible utility functions for encryption operations
 * Exports all utility functions for easy importing
 */

// Export K6-compatible utility functions
export {
  encodeToBase64,
  decodeFromBase64,
  stringToUint8Array,
  uint8ArrayToString,
  hexToUint8Array,
  uint8ArrayToHex
} from './k6-utils';

// Export certificate validation functions
export {
  validatePEMFormat,
  extractPEMContent,
  validateCertificateContent,
  validateCertificateFormat,
  splitMultipleCertificates,
  type CertificateValidationResult
} from './certificate-validation';

// Export encryption functions
export {
  parsePEMCertificate,
  validatePayloadSize,
  encryptWithRSAOAEP,
  payloadEncryptionFactory,
  validateEncryptionInputs,
  type EncryptionResult,
  type CertificateInfo
} from './encryption';