/**
 * Simple test utilities to verify K6-compatible functions work correctly
 * This is a basic validation script that can be run in K6 environment
 */

import {
  encodeToBase64,
  decodeFromBase64,
  stringToUint8Array,
  uint8ArrayToString,
  hexToUint8Array,
  uint8ArrayToHex,
  validatePEMFormat,
  extractPEMContent,
  validateCertificateFormat,
  splitMultipleCertificates
} from './index';

/**
 * Test the base64 encoding/decoding functions
 */
export function testBase64Functions(): boolean {
  console.log('Testing base64 functions...');
  
  try {
    // Test data
    const testString = 'Hello, K6 World!';
    const testData = stringToUint8Array(testString);
    
    // Encode to base64
    const encoded = encodeToBase64(testData);
    console.log(`Encoded: ${encoded}`);
    
    // Decode from base64
    const decoded = decodeFromBase64(encoded);
    const decodedString = uint8ArrayToString(decoded);
    
    console.log(`Decoded: ${decodedString}`);
    
    // Verify round-trip
    const success = decodedString === testString;
    console.log(`Base64 round-trip test: ${success ? 'PASS' : 'FAIL'}`);
    
    return success;
  } catch (error) {
    console.error('Base64 test failed:', error);
    return false;
  }
}

/**
 * Test the string/Uint8Array conversion functions
 */
export function testStringConversions(): boolean {
  console.log('Testing string conversion functions...');
  
  try {
    const testString = 'Test string with UTF-8: 🚀 ñáéíóú';
    
    // Convert to Uint8Array and back
    const uint8Array = stringToUint8Array(testString);
    const convertedBack = uint8ArrayToString(uint8Array);
    
    console.log(`Original: ${testString}`);
    console.log(`Converted back: ${convertedBack}`);
    
    const success = convertedBack === testString;
    console.log(`String conversion test: ${success ? 'PASS' : 'FAIL'}`);
    
    return success;
  } catch (error) {
    console.error('String conversion test failed:', error);
    return false;
  }
}

/**
 * Test the hex conversion functions
 */
export function testHexConversions(): boolean {
  console.log('Testing hex conversion functions...');
  
  try {
    const testHex = '48656c6c6f20576f726c64'; // "Hello World" in hex
    const expectedString = 'Hello World';
    
    // Convert hex to Uint8Array
    const uint8Array = hexToUint8Array(testHex);
    
    // Convert Uint8Array to string
    const resultString = uint8ArrayToString(uint8Array);
    
    // Convert back to hex
    const resultHex = uint8ArrayToHex(uint8Array);
    
    console.log(`Original hex: ${testHex}`);
    console.log(`Converted string: ${resultString}`);
    console.log(`Round-trip hex: ${resultHex}`);
    
    const success = resultString === expectedString && 
                   resultHex.toLowerCase() === testHex.toLowerCase();
    console.log(`Hex conversion test: ${success ? 'PASS' : 'FAIL'}`);
    
    return success;
  } catch (error) {
    console.error('Hex conversion test failed:', error);
    return false;
  }
}

/**
 * Test certificate validation functions
 */
export function testCertificateValidation(): boolean {
  console.log('Testing certificate validation functions...');
  
  try {
    // Valid PEM certificate format (sample)
    const validPEM = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBBQUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMTMwOTEyMjE1MjAyWhcNMTQwOTEyMjE1MjAyWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAwuqTiuGqAXGHYAg/WQwIE9+96jjCRHuwbobcTutMuepCHlWYenAPiGes
-----END CERTIFICATE-----`;

    // Invalid PEM certificate format
    const invalidPEM = 'This is not a certificate';
    
    // Test valid PEM
    const validResult = validatePEMFormat(validPEM);
    console.log(`Valid PEM test: ${validResult ? 'PASS' : 'FAIL'}`);
    
    // Test invalid PEM
    const invalidResult = !validatePEMFormat(invalidPEM);
    console.log(`Invalid PEM test: ${invalidResult ? 'PASS' : 'FAIL'}`);
    
    // Test PEM content extraction
    const content = extractPEMContent(validPEM);
    const contentTest = content !== null && content.length > 0;
    console.log(`PEM content extraction test: ${contentTest ? 'PASS' : 'FAIL'}`);
    
    // Test comprehensive validation
    const validation = validateCertificateFormat(validPEM);
    const validationTest = validation.isValid && validation.errors.length === 0;
    console.log(`Certificate validation test: ${validationTest ? 'PASS' : 'FAIL'}`);
    
    const success = validResult && invalidResult && contentTest && validationTest;
    console.log(`Certificate validation overall: ${success ? 'PASS' : 'FAIL'}`);
    
    return success;
  } catch (error) {
    console.error('Certificate validation test failed:', error);
    return false;
  }
}

/**
 * Run all utility function tests
 */
export function runAllTests(): boolean {
  console.log('=== Running K6 Utility Function Tests ===');
  
  const results = [
    testBase64Functions(),
    testStringConversions(),
    testHexConversions(),
    testCertificateValidation()
  ];
  
  const allPassed = results.every(result => result);
  
  console.log('=== Test Results ===');
  console.log(`All tests passed: ${allPassed ? 'YES' : 'NO'}`);
  console.log(`Passed: ${results.filter(r => r).length}/${results.length}`);
  
  return allPassed;
}