/**
 * Test script for RSA encryption functionality
 * This script tests the RSA-OAEP encryption implementation
 */

import {
  payloadEncryptionFactory,
  parsePEMCertificate,
  encryptWithRSAOAEP,
  validatePayloadSize,
  validateEncryptionInputs,
  validatePayloadInput,
  validateCertificateInput,
  comprehensiveValidation,
  EncryptionErrorType,
  formatEncryptionError,
  validatePayloadSafety,
  handleCircularReferences,
  payloadEncryptionFactoryWithErrors
} from './utils/encryption';

import { validateCertificateFormat } from './utils/certificate-validation';

// Sample RSA 2048-bit certificate for testing (self-signed, valid until 2030)
const TEST_CERTIFICATE = `-----B<PERSON>IN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==
-----END CERTIFICATE-----`;

// Test payload
const TEST_PAYLOAD = {
  pin: "1234",
  cardId: "****************",
  cardType: "VISA",
  otp: "123456"
};

/**
 * Test certificate parsing functionality (including expired certificates)
 */
function testCertificateParsing(): void {
  console.log('Testing certificate parsing...');

  const certInfo = parsePEMCertificate(TEST_CERTIFICATE);

  if (certInfo.valid) {
    console.log('✓ Certificate parsed successfully');
    console.log(`  Key size: ${certInfo.keySize} bits`);
    console.log(`  Algorithm: ${certInfo.algorithm}`);
    console.log(`  Issuer: ${certInfo.issuer}`);
    console.log(`  Subject: ${certInfo.subject}`);
    console.log(`  Expiration: ${certInfo.expirationDate}`);
    console.log(`  Is Expired: ${certInfo.isExpired}`);
  } else {
    console.log('✗ Certificate parsing failed');
    if (certInfo.isExpired) {
      console.log('  Reason: Certificate has expired');
      console.log(`  Expiration date: ${certInfo.expirationDate}`);
    }

    // Test parsing without expiration validation to verify core parsing works
    console.log('\nTesting core parsing functionality (ignoring expiration)...');
    testCertificateParsingIgnoreExpiration();
  }
}

/**
 * Test certificate parsing ignoring expiration for testing purposes
 */
function testCertificateParsingIgnoreExpiration(): void {
  try {
    // Use already imported functions
    const validation = validateCertificateFormat(TEST_CERTIFICATE);
    if (!validation.isValid) {
      console.log('✗ Certificate format validation failed');
      return;
    }

    console.log('✓ Core certificate parsing successful (ignoring expiration)');
    console.log('  Certificate format validation passed');
    console.log('  Note: Full parsing test requires jsrsasign which is bundled in the main functions');

  } catch (error) {
    console.log(`✗ Core parsing failed: ${error}`);
  }
}

/**
 * Test payload size validation
 */
function testPayloadSizeValidation(): void {
  console.log('\nTesting payload size validation...');

  // Test with 2048-bit key (should allow ~214 bytes)
  const keySize = 2048;
  const smallPayload = 100;
  const largePayload = 300;

  if (validatePayloadSize(smallPayload, keySize)) {
    console.log(`✓ Small payload (${smallPayload} bytes) validation passed`);
  } else {
    console.log(`✗ Small payload (${smallPayload} bytes) validation failed`);
  }

  if (!validatePayloadSize(largePayload, keySize)) {
    console.log(`✓ Large payload (${largePayload} bytes) correctly rejected`);
  } else {
    console.log(`✗ Large payload (${largePayload} bytes) should have been rejected`);
  }
}

/**
 * Test input validation
 */
function testInputValidation(): void {
  console.log('\nTesting input validation...');

  // Test valid inputs
  const validResult = validateEncryptionInputs(TEST_PAYLOAD, TEST_CERTIFICATE);
  if (validResult.success) {
    console.log('✓ Valid inputs accepted');
  } else {
    console.log(`✗ Valid inputs rejected: ${validResult.error}`);
  }

  // Test invalid payload
  const invalidPayloadResult = validateEncryptionInputs(null, TEST_CERTIFICATE);
  if (!invalidPayloadResult.success) {
    console.log('✓ Null payload correctly rejected');
  } else {
    console.log('✗ Null payload should have been rejected');
  }

  // Test invalid certificate
  const invalidCertResult = validateEncryptionInputs(TEST_PAYLOAD, 'invalid-cert');
  if (!invalidCertResult.success) {
    console.log('✓ Invalid certificate correctly rejected');
  } else {
    console.log('✗ Invalid certificate should have been rejected');
  }
}

/**
 * Test enhanced input validation and error handling
 */
function testEnhancedValidation(): void {
  console.log('\nTesting enhanced input validation and error handling...');

  // Test 1: Null payload validation
  console.log('  Test 1: Null payload validation');
  const nullPayloadResult = validatePayloadInput(null);
  if (!nullPayloadResult.success && nullPayloadResult.error?.code === EncryptionErrorType.INVALID_PAYLOAD) {
    console.log('  ✓ Null payload correctly rejected with proper error code');
  } else {
    console.log('  ✗ Null payload validation failed');
  }

  // Test 2: Empty object validation
  console.log('  Test 2: Empty object validation');
  const emptyObjectResult = validatePayloadInput({});
  if (!emptyObjectResult.success && emptyObjectResult.error?.code === EncryptionErrorType.INVALID_PAYLOAD) {
    console.log('  ✓ Empty object correctly rejected');
  } else {
    console.log('  ✗ Empty object validation failed');
  }

  // Test 3: Empty string validation
  console.log('  Test 3: Empty string validation');
  const emptyStringResult = validatePayloadInput('   ');
  if (!emptyStringResult.success && emptyStringResult.error?.code === EncryptionErrorType.INVALID_PAYLOAD) {
    console.log('  ✓ Empty string correctly rejected');
  } else {
    console.log('  ✗ Empty string validation failed');
  }

  // Test 4: Valid payload validation
  console.log('  Test 4: Valid payload validation');
  const validPayloadResult = validatePayloadInput(TEST_PAYLOAD);
  if (validPayloadResult.success) {
    console.log('  ✓ Valid payload accepted');
  } else {
    console.log('  ✗ Valid payload validation failed');
  }

  // Test 5: Certificate validation - empty
  console.log('  Test 5: Certificate validation - empty');
  const emptyCertResult = validateCertificateInput('');
  if (!emptyCertResult.success && emptyCertResult.error?.code === EncryptionErrorType.INVALID_CERTIFICATE) {
    console.log('  ✓ Empty certificate correctly rejected');
  } else {
    console.log('  ✗ Empty certificate validation failed');
  }

  // Test 6: Certificate validation - wrong type
  console.log('  Test 6: Certificate validation - wrong type');
  const wrongTypeCertResult = validateCertificateInput(123 as any);
  if (!wrongTypeCertResult.success && wrongTypeCertResult.error?.code === EncryptionErrorType.INVALID_CERTIFICATE) {
    console.log('  ✓ Wrong type certificate correctly rejected');
  } else {
    console.log('  ✗ Wrong type certificate validation failed');
  }

  // Test 7: Circular reference handling
  console.log('  Test 7: Circular reference handling');
  const circularObj: any = { name: 'test' };
  circularObj.self = circularObj;
  
  try {
    const handled = handleCircularReferences(circularObj);
    if (handled && handled.name === 'test' && handled.self === '[Circular Reference]') {
      console.log('  ✓ Circular reference handled correctly');
    } else {
      console.log('  ✗ Circular reference handling failed');
    }
  } catch (error) {
    console.log('  ✗ Error handling circular reference:', error);
  }

  // Test 8: Payload safety validation
  console.log('  Test 8: Payload safety validation');
  const unsafePayload = { script: '<script>alert("xss")</script>', data: 'normal' };
  const safetyResult = validatePayloadSafety(unsafePayload);
  if (!safetyResult.isSafe && safetyResult.issues.length > 0) {
    console.log('  ✓ Unsafe payload correctly identified');
  } else {
    console.log('  ✗ Unsafe payload validation failed');
  }

  // Test 9: Large payload validation
  console.log('  Test 9: Large payload validation');
  const veryLargePayload = { data: 'x'.repeat(50000) }; // Very large payload
  const veryLargePayloadResult = validatePayloadInput(veryLargePayload);
  if (!veryLargePayloadResult.success && veryLargePayloadResult.error?.code === EncryptionErrorType.PAYLOAD_TOO_LARGE) {
    console.log('  ✓ Very large payload correctly rejected');
  } else {
    console.log('  ✗ Very large payload validation failed');
  }

  // Test 10: Comprehensive validation
  console.log('  Test 10: Comprehensive validation');
  const comprehensiveResult = comprehensiveValidation(TEST_PAYLOAD, 'invalid-cert');
  if (!comprehensiveResult.isValid && comprehensiveResult.errors.length > 0) {
    console.log('  ✓ Comprehensive validation correctly identified errors');
  } else {
    console.log('  ✗ Comprehensive validation failed');
  }

  // Test 11: Error formatting
  console.log('  Test 11: Error formatting');
  if (nullPayloadResult.error) {
    const formattedError = formatEncryptionError(nullPayloadResult.error);
    if (formattedError.includes(EncryptionErrorType.INVALID_PAYLOAD)) {
      console.log('  ✓ Error formatting works correctly');
    } else {
      console.log('  ✗ Error formatting failed');
    }
  }

  console.log('Enhanced validation tests completed');
}

/**
 * Test enhanced encryption function with detailed errors
 */
async function testEnhancedEncryption(): Promise<void> {
  console.log('\nTesting enhanced encryption with detailed errors...');

  try {
    // Test with valid inputs
    const validResult = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, TEST_CERTIFICATE);
    if (validResult.success) {
      console.log('✓ Enhanced encryption with valid inputs successful');
    } else {
      console.log('✗ Enhanced encryption with valid inputs failed:', formatEncryptionError(validResult.error!));
    }

    // Test with invalid payload
    const invalidPayloadResult = await payloadEncryptionFactoryWithErrors(null, TEST_CERTIFICATE);
    if (!invalidPayloadResult.success && invalidPayloadResult.error) {
      console.log('✓ Enhanced encryption correctly rejected invalid payload');
      console.log('  Error:', formatEncryptionError(invalidPayloadResult.error));
    } else {
      console.log('✗ Enhanced encryption should have rejected invalid payload');
    }

    // Test with invalid certificate
    const invalidCertResult = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, 'invalid');
    if (!invalidCertResult.success && invalidCertResult.error) {
      console.log('✓ Enhanced encryption correctly rejected invalid certificate');
      console.log('  Error:', formatEncryptionError(invalidCertResult.error));
    } else {
      console.log('✗ Enhanced encryption should have rejected invalid certificate');
    }

  } catch (error) {
    console.log(`✗ Enhanced encryption test error: ${error}`);
  }
}

/**
 * Test RSA encryption
 */
async function testRSAEncryption(): Promise<void> {
  console.log('\nTesting RSA encryption...');

  try {
    // Parse certificate first
    const certInfo = parsePEMCertificate(TEST_CERTIFICATE);
    if (!certInfo.valid) {
      console.log('✗ Cannot test encryption - certificate parsing failed');
      return;
    }

    // Test direct encryption
    const message = JSON.stringify(TEST_PAYLOAD);
    const encrypted = encryptWithRSAOAEP(message, certInfo.publicKey);

    if (encrypted) {
      console.log('✓ RSA-OAEP encryption successful');
      console.log(`  Encrypted data length: ${encrypted.length} characters`);
      console.log(`  Encrypted data (first 50 chars): ${encrypted.substring(0, 50)}...`);
    } else {
      console.log('✗ RSA-OAEP encryption failed');
    }
  } catch (error) {
    console.log(`✗ RSA encryption error: ${error}`);
  }
}

/**
 * Test complete encryption workflow
 */
async function testCompleteWorkflow(): Promise<void> {
  console.log('\nTesting complete encryption workflow...');

  try {
    const result = await payloadEncryptionFactory(TEST_PAYLOAD, TEST_CERTIFICATE);

    if (result) {
      console.log('✓ Complete encryption workflow successful');
      console.log(`  Result length: ${result.length} characters`);
      console.log(`  Result (first 50 chars): ${result.substring(0, 50)}...`);
    } else {
      console.log('✗ Complete encryption workflow failed');
    }
  } catch (error) {
    console.log(`✗ Complete workflow error: ${error}`);
  }
}

/**
 * Run all tests
 */
async function runTests(): Promise<void> {
  console.log('=== RSA Encryption Functionality Tests ===\n');

  testCertificateParsing();
  testPayloadSizeValidation();
  testInputValidation();
  testEnhancedValidation();
  await testRSAEncryption();
  await testEnhancedEncryption();
  await testCompleteWorkflow();

  console.log('\n=== Tests Complete ===');
}

// Export for K6 usage
export default runTests;

// Run tests if executed directly
if (typeof __VU === 'undefined') {
  runTests().catch(console.error);
}