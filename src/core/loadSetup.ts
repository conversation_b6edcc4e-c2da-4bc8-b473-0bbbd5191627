import { SharedArray } from "k6/data";
import { RunLoad, ScenarioLoad } from "../typings/globals";

export const virtualUsers = new SharedArray("virtual-users", function () {
  return [
    {
      type: "load",
      load: 6000,
    },
    {
      type: "endurance",
      load: 5000,
    },
    {
      type: "shock",
      load: 3000,
    },
  ] as RunLoad[];
});

export const maxVirtualUsers = virtualUsers.reduce(
  // return the largest load
  (max, vu) => (vu.load > max ? vu.load : max),
  0,
);

// Change this to reflect percentage of each scenario of total load in real life (bonus points to create feedback loop)
export const scenarioLoadPercentages = new SharedArray(
  "load-percentages",
  function () {
    return [
      {
        scenario: "accessHomePage",
        percentage: 0.41265,
      },
        {
          scenario: "accountTransaction",
          percentage: 0.125824,
        },
        {
          scenario: "accountTransactionDetails",
          percentage: 0.250397,
        },
        {
          scenario: "bookCertificateDeposit",
          percentage: 0.001252,
        },
        {
          scenario: "billPayment",
          percentage: 0.010016,
        },
        {
          scenario: "creditCardTransfer",
          percentage: 0.004382,
        },
        {
          scenario: "createTransferInsideCIB",
          percentage: 0.178052,
        },
        {
          scenario: "createOutSideCibTransfer",
          percentage: 0.178052,
        },
        {
          scenario: "creditCardInstallment",
          percentage: 0.003634,
        },
        {
          scenario: "creditCardTransaction",
          percentage: 0.041941,
        },
        {
          scenario: "depositDetails",
          percentage: 0.007512,
        },
        {
          scenario: "debitCardList",
          percentage: 0.017528,
        },
        {
          scenario: "ipnTransfer",
          percentage: 0.178052,
        },
        {
          scenario: "annexDownload",
          percentage: 0.000626,
        },
        {
            scenario: "cardManagement",
            percentage: 0.178052,
        },
        // {
        //     scenario: "debitCardIssuance",
        //     percentage: 0.000483727,
        // },
        // {
        //     scenario: "depositRedemption",
        //     percentage: 0.000483727,
        // }
        // {
        //   scenario: "estatementEnrollment",
        //   percentage: 0.045071,
        // },
        // {
        //   scenario:  "BiometricLogin",
        //   percentage: 0.192806,

        // },

   ];
  },
);

export const loadArray = new SharedArray("scenario-loads", function () {
  //map virtual users and scenarioLoadPercentages together to give {type, scenario, load=type.load*percentage}
  const loads: ScenarioLoad[] = [];
  virtualUsers.forEach((vu: RunLoad) => {
    scenarioLoadPercentages.forEach((scenario) => {
      loads.push({
        type: vu.type,
        scenario: scenario.scenario,
        load: Math.floor(
          vu.load *
            (scenarioLoadPercentages?.find(
              (s) => s.scenario == scenario.scenario,
            )?.percentage ?? 1),
        ),
      });
    });
  });
  return loads as ScenarioLoad[];
});
