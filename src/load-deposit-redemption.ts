import { setupVu } from "./core/setup";
import {depositRedemption , depositRedemptionExec} from "./scenarios/depositRedemption";

// Initialize the test environment
setupVu();

// Configure global options for the load test
export const options = {
    batch: 10,
    batchPerHost: 5,
    noVUConnectionReuse: true,
    scenarios: {
        userIpnTransfer: depositRedemption("smoke"),
    },
};

// Export the execution function for direct use
export { depositRedemptionExec };