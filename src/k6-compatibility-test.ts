/**
 * K6 Compatibility Validation Test
 * Validates that the encryption utility works correctly in K6 environment
 */

import { check, sleep } from 'k6';
import { Options } from 'k6/options';
import { Counter, Rate, Trend } from 'k6/metrics';
import {
  payloadEncryptionFactory,
  payloadEncryptionFactoryWithErrors,
  parsePEMCertificate
} from './utils/encryption';

// K6 test configuration for compatibility validation
export const options: Options = {
  scenarios: {
    compatibility_check: {
      executor: 'shared-iterations',
      vus: 1,
      iterations: 1,
      maxDuration: '60s',
      tags: { test_type: 'compatibility' }
    }
  },
  thresholds: {
    'compatibility_checks': ['rate>0.95'],
    'encryption_operations': ['rate>0.95']
  }
};

// Custom metrics
const compatibilityChecks = new Rate('compatibility_checks');
const encryptionOperations = new Rate('encryption_operations');
const k6Features = new Counter('k6_features_tested');

// Test certificate
const TEST_CERTIFICATE = `-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjQwMTAxMDAwMDAwWhcNMzAwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuExKvY1xzHFw4A9J9QnsdQQ+W3ESOoz/ZlzZIrb2EUfvn9+WBaKNqQd
n+J02RCXD98LbRAQlvV5aj2ExcTCqapzVe5TuSQoLlBLTSej/QjYotP6b1rQg6pd
ZfTAiOyf8eFdV1B2f+U5o9zerb8Sm9JFl6pG9RMapiHwYlERnW7g8hhXBQh4NBzi
KtQDdGqJJ2aBZgqRQgBuZxhQXGukEwUq1WiUZXQRyFDuHjn1ZSzQqFpQpkw6wsYa
+b9fyLTfvvBU8+vBjZkHtMxfNxvQDq9p+hW7rhUKwDx6xOfmRtlVgjVZ5LfvoHfH
5f7RxkUdXoVhgBE8s3X9zd5TY4VQIwIDAQABo1AwTjAdBgNVHQ4EFgQUhKs/VJ3I
WyKwrl0Ki5tNmhwu5b0wHwYDVR0jBBgwFoAUhKs/VJ3IWyKwrl0Ki5tNmhwu5b0w
DAYDVR0TBAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAWGbsCxlwjBHLV1M/Ux/O
kqiMcJmqbWCJoQYvYq6HEAEpHSE4zI7B8HONe0IYyb7dg8/odMnEyFtK9nRcAOqw
zUILccTviQUhXShYHb6MjMaLkxFNRp7FpO5KBFxmysCO7jaNcHmOmqY5RKBH4dOt
w6pEC1ANstEIN1VZUf7T3FuOSAFubsSkwW5hcfrLFecbvxGDd47/AH2Gpgk+23ZS
LPSkwjnMBI/R7F5EHB+RWJMdEJUOzFAHdC+1VQxStwxp9GJn/nZ1QiuqDkFNjwTr
VBFVo7jdnyWSHOW4F40Gkh5+5+YoQd3IUDG5uEcuuBDPQkCjXlcKMrEzF8xm7VgU
UQ==-----END CERTIFICATE-----`;

// Test payload
const TEST_PAYLOAD = {
  pin: "1234",
  cardId: "****************",
  cardType: "VISA",
  otp: "123456",
  timestamp: new Date().toISOString()
};

/**
 * Test K6 environment features
 */
function testK6Environment(): boolean {
  console.log('=== K6 Environment Compatibility Test ===');
  
  let allPassed = true;

  // Test 1: K6 global variables
  console.log('Test 1: K6 global variables');
  const k6GlobalsPassed = check(null, {
    '__VU is defined': () => typeof __VU !== 'undefined',
    '__VU is number': () => typeof __VU === 'number',
    '__ITER is defined': () => typeof __ITER !== 'undefined',
    '__ENV is defined': () => typeof __ENV !== 'undefined'
  });
  
  if (k6GlobalsPassed) {
    console.log(`  ✅ K6 globals available (VU: ${__VU}, Iter: ${__ITER})`);
    k6Features.add(1);
  } else {
    console.log('  ❌ K6 globals not available');
    allPassed = false;
  }
  
  compatibilityChecks.add(k6GlobalsPassed ? 1 : 0);

  // Test 2: JavaScript built-ins
  console.log('Test 2: JavaScript built-ins');
  const jsBuiltinsPassed = check(null, {
    'JSON available': () => typeof JSON !== 'undefined',
    'JSON.stringify works': () => {
      try {
        return JSON.stringify({ test: 'value' }) === '{"test":"value"}';
      } catch (e) {
        return false;
      }
    },
    'JSON.parse works': () => {
      try {
        const parsed = JSON.parse('{"test":"value"}');
        return parsed.test === 'value';
      } catch (e) {
        return false;
      }
    },
    'Date available': () => typeof Date !== 'undefined',
    'Date constructor works': () => {
      try {
        const date = new Date();
        return date instanceof Date;
      } catch (e) {
        return false;
      }
    }
  });
  
  if (jsBuiltinsPassed) {
    console.log('  ✅ JavaScript built-ins working');
    k6Features.add(1);
  } else {
    console.log('  ❌ JavaScript built-ins issues');
    allPassed = false;
  }
  
  compatibilityChecks.add(jsBuiltinsPassed ? 1 : 0);

  // Test 3: TypedArrays and binary data
  console.log('Test 3: TypedArrays and binary data');
  const typedArraysPassed = check(null, {
    'Uint8Array available': () => typeof Uint8Array !== 'undefined',
    'Uint8Array constructor works': () => {
      try {
        const arr = new Uint8Array([1, 2, 3]);
        return arr.length === 3 && arr[0] === 1;
      } catch (e) {
        return false;
      }
    },
    'TextEncoder available': () => typeof TextEncoder !== 'undefined',
    'TextEncoder works': () => {
      try {
        const encoder = new TextEncoder();
        const encoded = encoder.encode('test');
        return encoded instanceof Uint8Array && encoded.length === 4;
      } catch (e) {
        return false;
      }
    },
    'atob available': () => typeof atob !== 'undefined',
    'btoa available': () => typeof btoa !== 'undefined',
    'base64 encoding works': () => {
      try {
        const encoded = btoa('test');
        const decoded = atob(encoded);
        return decoded === 'test';
      } catch (e) {
        return false;
      }
    }
  });
  
  if (typedArraysPassed) {
    console.log('  ✅ TypedArrays and binary data working');
    k6Features.add(1);
  } else {
    console.log('  ❌ TypedArrays and binary data issues');
    allPassed = false;
  }
  
  compatibilityChecks.add(typedArraysPassed ? 1 : 0);

  // Test 4: Error handling
  console.log('Test 4: Error handling');
  const errorHandlingPassed = check(null, {
    'try-catch works': () => {
      try {
        throw new Error('test');
      } catch (e) {
        return e instanceof Error && e.message === 'test';
      }
    },
    'Error constructor works': () => {
      const err = new Error('test');
      return err instanceof Error && err.message === 'test';
    },
    'typeof operator works': () => {
      return typeof 'string' === 'string' && 
             typeof 123 === 'number' && 
             typeof {} === 'object';
    }
  });
  
  if (errorHandlingPassed) {
    console.log('  ✅ Error handling working');
    k6Features.add(1);
  } else {
    console.log('  ❌ Error handling issues');
    allPassed = false;
  }
  
  compatibilityChecks.add(errorHandlingPassed ? 1 : 0);

  return allPassed;
}

/**
 * Test encryption utility imports and basic functionality
 */
function testEncryptionUtilityImports(): boolean {
  console.log('=== Encryption Utility Import Test ===');
  
  let allPassed = true;

  // Test 1: Function imports
  console.log('Test 1: Function imports');
  const importsPassed = check(null, {
    'payloadEncryptionFactory imported': () => typeof payloadEncryptionFactory === 'function',
    'payloadEncryptionFactoryWithErrors imported': () => typeof payloadEncryptionFactoryWithErrors === 'function',
    'parsePEMCertificate imported': () => typeof parsePEMCertificate === 'function'
  });
  
  if (importsPassed) {
    console.log('  ✅ All functions imported successfully');
    k6Features.add(1);
  } else {
    console.log('  ❌ Function import issues');
    allPassed = false;
  }
  
  compatibilityChecks.add(importsPassed ? 1 : 0);

  // Test 2: Certificate parsing
  console.log('Test 2: Certificate parsing');
  let certParsingPassed = false;
  try {
    const certInfo = parsePEMCertificate(TEST_CERTIFICATE);
    certParsingPassed = check(certInfo, {
      'certificate parsing succeeds': (info) => info.valid === true,
      'public key extracted': (info) => info.publicKey !== null,
      'key size detected': (info) => (info.keySize || 0) >= 2048,
      'algorithm detected': (info) => info.algorithm === 'RSA'
    });
    
    if (certParsingPassed) {
      console.log('  ✅ Certificate parsing working');
      k6Features.add(1);
    } else {
      console.log('  ❌ Certificate parsing issues');
      allPassed = false;
    }
  } catch (error) {
    console.log(`  ❌ Certificate parsing error: ${error}`);
    allPassed = false;
  }
  
  compatibilityChecks.add(certParsingPassed ? 1 : 0);

  return allPassed;
}

/**
 * Test encryption operations in K6 environment
 */
async function testEncryptionOperations(): Promise<boolean> {
  console.log('=== Encryption Operations Test ===');
  
  let allPassed = true;

  // Test 1: Basic encryption
  console.log('Test 1: Basic encryption');
  try {
    const result = await payloadEncryptionFactory(TEST_PAYLOAD, TEST_CERTIFICATE);
    
    const basicEncryptionPassed = check(result, {
      'encryption returns result': (res) => res !== null,
      'result is string': (res) => typeof res === 'string',
      'result is base64': (res) => res ? /^[A-Za-z0-9+/]*={0,2}$/.test(res) : false,
      'result has correct length': (res) => res ? (res.length >= 340 && res.length <= 350) : false
    });
    
    if (basicEncryptionPassed) {
      console.log('  ✅ Basic encryption working');
      encryptionOperations.add(1);
      k6Features.add(1);
    } else {
      console.log('  ❌ Basic encryption issues');
      encryptionOperations.add(0);
      allPassed = false;
    }
    
    compatibilityChecks.add(basicEncryptionPassed ? 1 : 0);
  } catch (error) {
    console.log(`  ❌ Basic encryption error: ${error}`);
    encryptionOperations.add(0);
    compatibilityChecks.add(0);
    allPassed = false;
  }

  // Test 2: Enhanced encryption with error handling
  console.log('Test 2: Enhanced encryption with error handling');
  try {
    const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, TEST_CERTIFICATE);
    
    const enhancedEncryptionPassed = check(result, {
      'enhanced encryption succeeds': (res) => !!(res && res.success === true),
      'enhanced result has data': (res) => !!(res && res.data !== null),
      'enhanced data is base64': (res) => !!(res && res.data && /^[A-Za-z0-9+/]*={0,2}$/.test(res.data))
    });
    
    if (enhancedEncryptionPassed) {
      console.log('  ✅ Enhanced encryption working');
      encryptionOperations.add(1);
      k6Features.add(1);
    } else {
      console.log('  ❌ Enhanced encryption issues');
      if (result && !result.success && result.error) {
        console.log(`    Error: ${result.error.message}`);
      }
      encryptionOperations.add(0);
      allPassed = false;
    }
    
    compatibilityChecks.add(enhancedEncryptionPassed ? 1 : 0);
  } catch (error) {
    console.log(`  ❌ Enhanced encryption error: ${error}`);
    encryptionOperations.add(0);
    compatibilityChecks.add(0);
    allPassed = false;
  }

  // Test 3: Error handling validation
  console.log('Test 3: Error handling validation');
  try {
    const errorResult = await payloadEncryptionFactoryWithErrors(null, TEST_CERTIFICATE);
    
    const errorHandlingPassed = check(errorResult, {
      'null payload rejected': (res) => !!(res && res.success === false),
      'error object provided': (res) => !!(res && res.error !== undefined),
      'error has code': (res) => !!(res && res.error && res.error.code !== undefined),
      'error has message': (res) => !!(res && res.error && res.error.message !== undefined)
    });
    
    if (errorHandlingPassed) {
      console.log('  ✅ Error handling working');
      k6Features.add(1);
    } else {
      console.log('  ❌ Error handling issues');
      allPassed = false;
    }
    
    compatibilityChecks.add(errorHandlingPassed ? 1 : 0);
  } catch (error) {
    console.log(`  ❌ Error handling test error: ${error}`);
    compatibilityChecks.add(0);
    allPassed = false;
  }

  return allPassed;
}

/**
 * Test performance characteristics in K6
 */
function testPerformanceCharacteristics(): boolean {
  console.log('=== Performance Characteristics Test ===');
  
  let allPassed = true;

  // Test 1: Memory usage (basic check)
  console.log('Test 1: Memory usage check');
  const initialTime = Date.now();
  
  // Perform multiple operations to check for memory leaks
  const operations = [];
  for (let i = 0; i < 10; i++) {
    try {
      const payload = { ...TEST_PAYLOAD, id: i };
      const operation = payloadEncryptionFactoryWithErrors(payload, TEST_CERTIFICATE);
      operations.push(operation);
    } catch (error) {
      console.log(`  Operation ${i} failed: ${error}`);
    }
  }
  
  const duration = Date.now() - initialTime;
  
  const performancePassed = check(null, {
    'multiple operations complete quickly': () => duration < 5000, // 5 seconds max
    'operations array created': () => operations.length === 10
  });
  
  if (performancePassed) {
    console.log(`  ✅ Performance characteristics acceptable (${duration}ms for 10 operations)`);
    k6Features.add(1);
  } else {
    console.log(`  ❌ Performance issues (${duration}ms for 10 operations)`);
    allPassed = false;
  }
  
  compatibilityChecks.add(performancePassed ? 1 : 0);

  // Test 2: Concurrent operations simulation
  console.log('Test 2: Concurrent operations simulation');
  const concurrentStart = Date.now();
  
  // Simulate concurrent operations by creating multiple promises
  const concurrentOperations = [];
  for (let i = 0; i < 5; i++) {
    const payload = { ...TEST_PAYLOAD, concurrent_id: i };
    concurrentOperations.push(
      payloadEncryptionFactoryWithErrors(payload, TEST_CERTIFICATE)
    );
  }
  
  const concurrentDuration = Date.now() - concurrentStart;
  
  const concurrentPassed = check(null, {
    'concurrent operations setup quickly': () => concurrentDuration < 1000, // 1 second max
    'concurrent operations created': () => concurrentOperations.length === 5
  });
  
  if (concurrentPassed) {
    console.log(`  ✅ Concurrent operations setup acceptable (${concurrentDuration}ms)`);
    k6Features.add(1);
  } else {
    console.log(`  ❌ Concurrent operations setup issues (${concurrentDuration}ms)`);
    allPassed = false;
  }
  
  compatibilityChecks.add(concurrentPassed ? 1 : 0);

  return allPassed;
}

/**
 * Test output format compatibility with PKCS#7-like expectations
 */
async function testOutputFormatCompatibility(): Promise<boolean> {
  console.log('=== Output Format Compatibility Test ===');
  
  let allPassed = true;

  try {
    const result = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, TEST_CERTIFICATE);
    
    if (!result || !result.success || !result.data) {
      console.log('  ❌ Cannot test output format - encryption failed');
      compatibilityChecks.add(0);
      return false;
    }
    
    const encryptedData = result.data;
    
    // Test 1: Base64 format validation
    console.log('Test 1: Base64 format validation');
    const base64Passed = check(encryptedData, {
      'output is valid base64': (data) => /^[A-Za-z0-9+/]*={0,2}$/.test(data),
      'base64 length is correct': (data) => data.length >= 340 && data.length <= 350,
      'base64 padding is correct': (data) => {
        const paddingCount = (data.match(/=/g) || []).length;
        return paddingCount <= 2;
      }
    });
    
    if (base64Passed) {
      console.log('  ✅ Base64 format validation passed');
      k6Features.add(1);
    } else {
      console.log('  ❌ Base64 format validation failed');
      allPassed = false;
    }
    
    compatibilityChecks.add(base64Passed ? 1 : 0);

    // Test 2: Binary data characteristics
    console.log('Test 2: Binary data characteristics');
    try {
      const decodedBytes = atob(encryptedData);
      
      const binaryPassed = check(null, {
        'decoded data has correct length': () => decodedBytes.length === 256, // RSA 2048 = 256 bytes
        'decoded data appears binary': () => {
          // Check for non-printable characters (typical of encrypted data)
          let nonPrintableCount = 0;
          for (let i = 0; i < Math.min(decodedBytes.length, 50); i++) {
            const charCode = decodedBytes.charCodeAt(i);
            if (charCode < 32 || charCode > 126) {
              nonPrintableCount++;
            }
          }
          return nonPrintableCount > 10;
        },
        'decoded data is not empty': () => decodedBytes.length > 0
      });
      
      if (binaryPassed) {
        console.log('  ✅ Binary data characteristics correct');
        k6Features.add(1);
      } else {
        console.log('  ❌ Binary data characteristics incorrect');
        allPassed = false;
      }
      
      compatibilityChecks.add(binaryPassed ? 1 : 0);
    } catch (error) {
      console.log(`  ❌ Binary data test error: ${error}`);
      compatibilityChecks.add(0);
      allPassed = false;
    }

    // Test 3: Consistency check
    console.log('Test 3: Consistency check');
    try {
      const secondResult = await payloadEncryptionFactoryWithErrors(TEST_PAYLOAD, TEST_CERTIFICATE);
      
      const consistencyPassed = check(null, {
        'second encryption succeeds': () => secondResult && secondResult.success,
        'results have same format': () => {
          if (!secondResult || !secondResult.data) return false;
          return secondResult.data.length === encryptedData.length;
        },
        'results are properly formatted': () => {
          if (!secondResult || !secondResult.data) return false;
          return /^[A-Za-z0-9+/]*={0,2}$/.test(secondResult.data);
        }
      });
      
      if (consistencyPassed) {
        console.log('  ✅ Consistency check passed');
        k6Features.add(1);
      } else {
        console.log('  ❌ Consistency check failed');
        allPassed = false;
      }
      
      compatibilityChecks.add(consistencyPassed ? 1 : 0);
    } catch (error) {
      console.log(`  ❌ Consistency test error: ${error}`);
      compatibilityChecks.add(0);
      allPassed = false;
    }

  } catch (error) {
    console.log(`  ❌ Output format test error: ${error}`);
    compatibilityChecks.add(0);
    allPassed = false;
  }

  return allPassed;
}

/**
 * Main test function
 */
export default async function (): Promise<void> {
  console.log('\n=== K6 Encryption Utility Compatibility Validation ===');
  console.log(`Running on VU: ${__VU}, Iteration: ${__ITER}`);
  
  let allTestsPassed = true;

  // Run all compatibility tests
  const environmentPassed = testK6Environment();
  const importsPassed = testEncryptionUtilityImports();
  const operationsPassed = await testEncryptionOperations();
  const performancePassed = testPerformanceCharacteristics();
  const formatPassed = await testOutputFormatCompatibility();

  allTestsPassed = environmentPassed && importsPassed && operationsPassed && 
                   performancePassed && formatPassed;

  // Final compatibility check
  const finalCheck = check(null, {
    'all compatibility tests passed': () => allTestsPassed,
    'minimum features tested': () => true // Features are tracked via add() calls
  });

  if (finalCheck) {
    console.log('\n✅ K6 Compatibility Validation PASSED');
    console.log('Features tested: Multiple K6 compatibility features');
  } else {
    console.log('\n❌ K6 Compatibility Validation FAILED');
    console.log('Features tested: Multiple K6 compatibility features');
  }

  // Small delay to ensure all async operations complete
  sleep(0.1);
}

/**
 * Setup function
 */
export function setup(): any {
  console.log('Setting up K6 compatibility validation...');
  
  // Basic environment check
  if (typeof __VU === 'undefined') {
    console.log('Warning: Not running in K6 environment');
  } else {
    console.log(`K6 environment detected (VU: ${__VU})`);
  }
  
  return { setupComplete: true };
}

/**
 * Teardown function
 */
export function teardown(data: any): void {
  console.log('\nK6 compatibility validation completed');
  
  if (data && data.setupComplete) {
    console.log('✅ All tests completed successfully');
  }
}